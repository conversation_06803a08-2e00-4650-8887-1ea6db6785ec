/**
 * 执行计划生成模块
 *
 * 主要功能：
 * 1. 根据用户输入和意图类型生成智能执行计划
 * 2. 使用 AI 分析用户需求，生成最优的工具调用序列
 * 3. 支持动态参数解析和上下文关联
 * 4. 提供执行时间估算和性能优化
 *
 * 核心特性：
 * - AI 驱动的计划生成：使用豆包 AI 分析用户意图，生成智能计划
 * - 工具自动选择：从工具注册表中智能选择最合适的工具组合
 * - 参数智能推导：支持静态参数和动态参数的自动解析
 * - 执行优化：预估执行时间，优化步骤顺序和资源使用
 *
 * 技术架构：
 * - 基于 OpenAI SDK 与豆包 AI 进行交互
 * - 采用 JSON 格式的结构化计划描述
 * - 集成工具注册表进行工具配置管理
 * - 支持计划的序列化和持久化存储
 *
 * 设计模式：
 * - 策略模式：不同意图类型对应不同的生成策略
 * - 工厂模式：动态创建执行步骤和参数配置
 * - 建造者模式：逐步构建复杂的执行计划对象
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

const OpenAI = require('openai')
const { doubaoParams, TOOL_REGISTRY } = require('./config')

// SimpleExecutionPlanner 已被移除，因为不再使用
// 现在统一使用 IntelligentExecutionPlanner 进行计划生成
// 这样可以确保所有计划都具备智能分析能力

/**
 * 智能执行计划生成器
 * 使用 AI 生成更复杂和智能的执行计划
 *
 * 核心能力：
 * - 自然语言理解：深度分析用户输入的语义和意图
 * - 智能工具选择：根据任务需求自动选择最优工具组合
 * - 参数智能推导：自动推导工具调用所需的参数
 * - 执行优化：优化步骤顺序，提高执行效率
 *
 * 生成流程：
 * 1. 构建 AI 分析提示词
 * 2. 调用豆包 AI 进行意图分析和计划生成
 * 3. 解析 AI 返回的 JSON 格式计划
 * 4. 构建标准化的执行步骤对象
 * 5. 计算执行时间和依赖关系
 */
class IntelligentExecutionPlanner {
  /**
   * 生成执行计划
   * 这是计划生成器的核心方法，使用 AI 分析用户意图并生成智能执行计划
   *
   * @param {string} userInput - 用户输入内容，用于 AI 分析和参数推导
   * @param {string} intentType - 意图类型：task
   * @returns {Object} 完整的执行计划对象
   *
   * 执行计划对象结构：
   * - planId: 计划唯一标识（UUID）
   * - userInput: 用户原始输入
   * - intentType: 识别的意图类型
   * - steps: 执行步骤数组
   * - totalSteps: 总步骤数
   * - status: 计划状态（pending/running/completed/failed）
   * - startTime: 计划创建时间戳
   * - estimatedTotalTime: 预估总执行时间（毫秒）
   *
   * 生成流程：
   * 1. 初始化执行计划基础结构
   * 2. 构建 AI 分析提示词
   * 3. 调用豆包 AI 进行智能分析
   * 4. 解析 AI 返回的 JSON 计划数据
   * 5. 构建标准化的执行步骤对象
   * 6. 计算总执行时间和步骤数量
   */
  static async generatePlan(userInput, intentType) {
    // 初始化执行计划的基础结构
    const executionPlan = {
      planId: this.generateUUID(), // 生成唯一的计划标识
      userInput: userInput, // 保存用户原始输入，用于后续分析
      intentType: intentType, // 保存意图类型，用于计划分类
      steps: [], // 执行步骤数组，将由 AI 生成填充
      totalSteps: 0, // 总步骤数，在步骤构建完成后计算
      status: 'pending', // 计划状态，初始为待执行
      startTime: Date.now(), // 计划创建时间戳
      estimatedTotalTime: 0, // 预估总执行时间，在步骤构建完成后计算
    }

    // 使用 AI 生成更智能的执行计划
    // 构建专门的分析提示词，指导 AI 理解用户意图并生成合适的计划
    const analysisPrompt = this.buildAnalysisPrompt(userInput, intentType)

    try {
      // 调用豆包 AI 进行智能分析，获取结构化的计划数据
      const aiResponse = await this.callAI(analysisPrompt)
      const planData = JSON.parse(aiResponse) // 解析 AI 返回的 JSON 格式计划

      // 构建执行步骤
      // 遍历 AI 生成的步骤数据，构建标准化的执行步骤对象
      for (let i = 0; i < planData.steps.length; i++) {
        const stepData = planData.steps[i] // AI 生成的步骤数据
        const toolConfig = TOOL_REGISTRY[stepData.toolName] // 从工具注册表获取工具配置

        // 构建标准化的执行步骤对象
        const step = {
          stepId: this.generateUUID(), // 生成唯一的步骤标识
          toolName: stepData.toolName, // 要调用的工具名称，必须在工具注册表中存在
          description: stepData.description, // 步骤描述，说明该步骤的作用和目的
          parameters: stepData.parameters, // 工具调用参数，可能包含静态值和动态引用
          dependencies: stepData.dependencies || [], // 步骤依赖关系，指定前置步骤的 stepId
          status: 'pending', // 步骤状态：pending/running/completed/failed
          retryCount: 0, // 当前重试次数，用于错误恢复
          maxRetries: 3, // 最大重试次数，超过后标记为失败
          executionTime: null, // 实际执行时间，执行完成后填充
          estimatedTime: toolConfig?.metadata?.estimatedTime || 2000, // 预估执行时间（毫秒）
          error: null, // 错误信息，执行失败时填充
        }

        // 将构建好的步骤添加到执行计划中
        executionPlan.steps.push(step)
        // 累加预估总执行时间，用于进度预测
        executionPlan.estimatedTotalTime += step.estimatedTime
      }

      // 设置总步骤数，用于进度计算
      executionPlan.totalSteps = executionPlan.steps.length
      return executionPlan
    } catch (error) {
      // 错误处理：AI 计划解析失败时的降级策略
      console.warn('AI执行计划解析失败，使用默认计划:', error)
      // 使用默认计划生成器作为备选方案，确保系统可用性
      return this.generateDefaultPlan(userInput, intentType)
    }
  }

  /**
   * 提取项目关键词
   * 从用户输入中提取项目相关的关键词，用于智能项目匹配
   *
   * @param {string} input - 用户输入文本
   * @returns {string} 提取的项目关键词，未找到时返回空字符串
   *
   * 提取规则：
   * - 匹配 "XXX项目" 或 "XXXproject" 格式
   * - 提取其中的项目名称部分
   * - 支持中英文混合输入
   *
   * 使用场景：
   * - 用户创建任务时指定目标项目
   * - 查询任务时确定搜索范围
   * - 智能项目推荐和匹配
   */
  static extractProjectKeyword(input) {
    // 使用正则表达式匹配项目相关的表达式
    const matches = input.match(/(\w+)项目|(\w+)project/gi)
    if (matches && matches.length > 0) {
      // 移除 "项目" 或 "project" 后缀，提取核心关键词
      return matches[0].replace(/项目|project/gi, '')
    }
    return '' // 未找到项目关键词时返回空字符串
  }

  /**
   * 构建 AI 分析提示词
   * 为豆包 AI 构建专门的分析提示词，指导其生成结构化的执行计划
   *
   * @param {string} userInput - 用户输入内容
   * @param {string} intentType - 意图类型
   * @returns {string} 完整的 AI 分析提示词
   *
   * 提示词结构：
   * 1. 角色定义：定义 AI 的角色和职责
   * 2. 工具说明：提供可用工具的详细信息
   * 3. 任务要求：明确分析任务和输出格式
   * 4. 示例说明：提供标准的输出格式示例
   *
   * 设计原则：
   * - 明确的角色定位，确保 AI 理解其职责
   * - 详细的工具信息，帮助 AI 做出正确选择
   * - 标准化的输出格式，便于后续解析
   * - 丰富的示例，提高生成质量
   */
  static buildAnalysisPrompt(userInput, intentType) {
    // 生成工具信息提示词，让 AI 了解可用的工具和参数
    const toolPrompt = this.generateToolPrompt(TOOL_REGISTRY)

    return `分析用户输入："${userInput}"
意图类型：${intentType}

${toolPrompt}

请生成执行计划，支持以下动态参数引用：
- $context.key: 引用上下文数据
- $step.stepId.path: 引用前置步骤的结果
- $filter(stepId.path, condition): 对数据进行筛选

返回 JSON 格式：
{
  "analysis": "用户意图分析",
  "steps": [
    {
      "toolName": "工具名称",
      "description": "步骤描述",
      "parameters": {
        "param1": "静态值",
        "param2": "$context.targetProject.id"
      },
      "dependencies": ["step1"],
      "reasoning": "选择此工具的原因"
    }
  ]
}`
  }

  static generateToolPrompt(toolRegistry) {
    let prompt = '可用工具列表：\n'
    for (const [toolName, config] of Object.entries(toolRegistry)) {
      prompt += `- ${toolName}: ${config.description}\n`
      prompt += `  参数：${JSON.stringify(config.parameters)}\n`
    }
    return prompt
  }

  static async callAI(prompt) {
    // 调用豆包 AI 生成执行计划
    const openai = new OpenAI(doubaoParams)
    const response = await openai.chat.completions.create({
      messages: [
        { role: 'system', content: '你是一个专业的任务执行计划生成器。' },
        { role: 'user', content: prompt },
      ],
      model: 'doubao-seed-1-6-250615',
      stream: false,
      timeout: 30000,
    })

    return response.choices[0].message.content
  }

  static generateDefaultPlan(userInput, intentType) {
    // 当 AI 生成失败时的默认计划
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now(),
      estimatedTotalTime: 0,
    }

    // 基于规则的默认计划生成
    if (intentType === 'task') {
      // 为 task 类型生成通用的默认计划
      // 具体的任务类型将由执行计划生成器根据用户输入智能判断
      const stepId = this.generateUUID()
      executionPlan.steps = [
        {
          stepId: stepId,
          toolName: 'getTasks', // 默认先获取任务列表作为上下文
          description: '获取任务上下文信息',
          parameters: { limit: 10 },
          dependencies: [],
          status: 'pending',
          estimatedTime: 2000,
        },
      ]
      executionPlan.estimatedTotalTime = 2000
    }

    executionPlan.totalSteps = executionPlan.steps.length
    return executionPlan
  }

  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0
      const v = c == 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }
}

module.exports = {
  IntelligentExecutionPlanner, // 统一使用智能执行计划生成器
}
