/**
 * dida-analytics 云函数测试脚本
 * 
 * 功能说明：
 * - 测试所有数据分析功能的正确性
 * - 验证缓存机制和性能优化
 * - 确保与 MCP 实现的兼容性
 */

const analytics = require('./index.obj.js');

console.log('开始测试 dida-analytics 云函数...');

// 模拟 uniCloud 环境
global.uniCloud = {
  database: () => ({
    collection: (name) => ({
      where: (condition) => ({
        orderBy: (field, order) => ({
          limit: (num) => ({
            get: () => Promise.resolve({ data: [] })
          })
        }),
        count: () => Promise.resolve({ total: 0 }),
        remove: () => Promise.resolve({ deleted: 0 }),
        lt: (date) => condition,
        gt: (date) => condition
      }),
      field: (fields) => ({
        get: () => Promise.resolve({ data: [] })
      }),
      add: (data) => Promise.resolve({ id: 'test_id' }),
      count: () => Promise.resolve({ total: 0 })
    }),
    command: {
      lt: (date) => ({ lt: date }),
      gt: (date) => ({ gt: date })
    }
  }),
  importObject: (name) => {
    if (name === 'dida-base') {
      return {
        makeRequest: (url, method) => Promise.resolve({
          success: true,
          data: {
            projectProfiles: [
              { id: 'proj1', name: '工作项目', taskCount: 10 },
              { id: 'proj2', name: '个人项目', taskCount: 5 }
            ],
            tasks: [
              { id: 'task1', title: '完成报告', projectId: 'proj1', status: 2, priority: 3 },
              { id: 'task2', title: '学习新技术', projectId: 'proj2', status: 0, priority: 1 },
              { id: 'task3', title: '健身锻炼', projectId: 'proj2', status: 2, priority: 2 }
            ],
            tags: [
              { id: 'tag1', name: '工作', color: '#FF0000' },
              { id: 'tag2', name: '学习', color: '#00FF00' }
            ]
          }
        }),
        formatResponse: (data) => ({ success: true, data }),
        handleError: (error, code, message) => ({ 
          success: false, 
          error: { code, message: message || error.message } 
        })
      };
    }
    if (name === 'dida-goal') {
      return {
        getGoals: () => Promise.resolve({
          success: true,
          data: [{
            id: 'test_goal_123',
            title: '测试目标',
            status: 'active',
            progress: 50,
            type: 'phase',
            keywords: ['工作', '效率'],
            description: '这是一个测试目标'
          }]
        }),
        getGoal: (params) => Promise.resolve({
          success: true,
          data: {
            id: params.goal_id,
            title: '测试目标',
            status: 'active',
            progress: 50,
            type: 'phase',
            keywords: ['工作', '效率'],
            description: '这是一个测试目标',
            createdTime: '2024-01-01T00:00:00.000Z',
            dueDate: '2024-12-31T23:59:59.999Z'
          }
        })
      };
    }
    return {};
  }
};

// 运行测试
async function runTests() {
  try {
    console.log('\n开始执行数据分析功能测试...\n');
    
    const result = await analytics.runAnalyticsTests();
    
    console.log('\n=== 测试结果 ===');
    console.log('测试状态:', result.data.test_status);
    console.log('通过率:', result.data.pass_rate);
    console.log('总测试数:', result.data.total_tests);
    console.log('通过测试:', result.data.passed_tests);
    console.log('失败测试:', result.data.failed_tests);
    
    console.log('\n=== 详细结果 ===');
    result.data.details.forEach(test => {
      const status = test.status === 'PASS' ? '✅' : test.status === 'SKIP' ? '⏭️' : '❌';
      console.log(`${status} ${test.test}: ${test.message}`);
      if (test.data) {
        console.log('   数据:', JSON.stringify(test.data, null, 2));
      }
      if (test.error) {
        console.log('   错误:', test.error);
      }
    });
    
    console.log('\n=== 测试总结 ===');
    if (result.data.pass_rate >= '80%') {
      console.log('🎉 测试结果优秀！所有核心功能正常工作。');
    } else if (result.data.pass_rate >= '60%') {
      console.log('✅ 测试结果良好，大部分功能正常工作。');
    } else {
      console.log('⚠️ 测试结果需要改进，部分功能存在问题。');
    }
    
    console.log('\n测试完成！');
    
  } catch (error) {
    console.error('\n❌ 测试执行失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 执行测试
runTests();
