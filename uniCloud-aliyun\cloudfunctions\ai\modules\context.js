/**
 * 执行上下文管理模块
 *
 * 主要功能：
 * 1. 管理任务执行过程中的所有数据和状态信息
 * 2. 提供步骤结果的存储和检索功能
 * 3. 自动提取和分析上下文数据
 * 4. 支持智能项目匹配和关键词提取
 * 5. 维护执行过程的元数据信息
 *
 * 核心特性：
 * - 会话隔离：每个执行会话都有独立的上下文
 * - 智能提取：自动从步骤结果中提取有用信息
 * - 项目匹配：基于用户输入智能匹配目标项目
 * - 数据关联：建立步骤间的数据依赖关系
 *
 * 使用场景：
 * - 多步骤任务执行时的数据传递
 * - 动态参数解析时的数据源
 * - 执行结果的统计和分析
 * - 用户意图的上下文理解
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

/**
 * 执行上下文管理器
 * 用于管理任务执行过程中的所有数据和状态信息
 *
 * 数据结构：
 * - stepResults: Map<stepId, {result, metadata}> 存储步骤执行结果
 * - contextData: Map<key, value> 存储提取的上下文数据
 * - metadata: Object 存储执行过程的元数据信息
 *
 * 核心功能：
 * - 步骤结果管理：存储和检索每个步骤的执行结果
 * - 上下文数据提取：自动从结果中提取关键信息
 * - 项目智能匹配：基于用户输入匹配最相关的项目
 * - 关键词提取：从用户输入中提取项目相关关键词
 */
class ExecutionContextManager {
  /**
   * 构造函数 - 初始化执行上下文管理器
   *
   * @param {string} sessionId - 会话唯一标识，用于区分不同的执行会话
   * @param {string} userInput - 用户输入的原始消息，用于上下文分析和项目匹配
   */
  constructor(sessionId, userInput) {
    this.sessionId = sessionId // 会话 ID，确保不同会话的数据隔离
    this.userInput = userInput // 用户原始输入，用于智能分析和关键词提取
    this.stepResults = new Map() // 存储各步骤的执行结果，key 为 stepId
    this.contextData = new Map() // 存储提取的上下文数据，key 为数据类型
    this.metadata = {
      startTime: Date.now(), // 执行开始时间戳
      currentStep: 0, // 当前执行步骤索引
      totalSteps: 0, // 总步骤数（在执行计划确定后设置）
    }
  }

  /**
   * 设置步骤执行结果
   * 存储指定步骤的执行结果，并自动提取上下文数据
   *
   * @param {string} stepId - 步骤唯一标识，用于后续检索
   * @param {Object} result - 步骤执行结果，包含工具调用的返回数据
   * @param {Object} metadata - 附加元数据，可选参数
   *
   * 功能特性：
   * - 自动添加时间戳和步骤索引
   * - 触发上下文数据的自动提取
   * - 支持元数据的扩展存储
   */
  setStepResult(stepId, result, metadata = {}) {
    this.stepResults.set(stepId, {
      result, // 原始执行结果
      metadata: {
        ...metadata, // 用户提供的元数据
        timestamp: Date.now(), // 记录结果存储时间
        stepIndex: this.metadata.currentStep, // 记录当前步骤索引
      },
    })
    // 自动从结果中提取有用的上下文数据
    // 这是智能数据提取的关键步骤
    this.extractContextData(stepId, result)
  }

  /**
   * 获取步骤执行结果
   * 根据步骤 ID 检索之前存储的执行结果
   *
   * @param {string} stepId - 步骤唯一标识
   * @returns {Object|undefined} 步骤执行结果，不存在时返回 undefined
   *
   * 使用场景：
   * - 后续步骤需要引用前面步骤的结果
   * - 动态参数解析时获取数据源
   * - 执行结果的统计和分析
   */
  getStepResult(stepId) {
    return this.stepResults.get(stepId)?.result
  }

  /**
   * 设置上下文数据
   * 手动设置特定键的上下文数据
   *
   * @param {string} key - 数据键名
   * @param {any} value - 数据值
   *
   * 使用场景：
   * - 手动添加特定的上下文信息
   * - 覆盖自动提取的数据
   * - 添加计算得出的衍生数据
   */
  setContextData(key, value) {
    this.contextData.set(key, value)
  }

  /**
   * 获取上下文数据
   * 根据键名获取提取的上下文数据
   *
   * @param {string} key - 数据键名，如 'projects'、'tasks' 等
   * @returns {any} 数据值，不存在时返回 undefined
   *
   * 常用键名：
   * - 'projects': 项目列表数据
   * - 'tasks': 任务列表数据
   * - 'completedTasks': 已完成任务列表
   * - 'uncompletedTasks': 未完成任务列表
   */
  getContextData(key) {
    return this.contextData.get(key)
  }

  /**
   * 从步骤结果中提取有用的上下文数据
   * 自动识别项目信息、任务统计等关键数据
   * @param {string} stepId - 步骤标识（用于日志记录）
   * @param {Object} result - 步骤执行结果
   */
  extractContextData(stepId, result) {
    // 处理项目列表数据
    if (result.data && Array.isArray(result.data)) {
      const targetProject = this.findTargetProject(result.data)
      if (targetProject) {
        this.setContextData('targetProject', targetProject)
        console.log(`从步骤 ${stepId} 提取到目标项目:`, targetProject.name)
      }
    }

    // 处理任务列表数据
    if (result.tasks && Array.isArray(result.tasks)) {
      this.setContextData('taskCount', result.tasks.length)
      this.setContextData(
        'uncompletedTasks',
        result.tasks.filter((t) => !t.completed)
      )
      console.log(`从步骤 ${stepId} 提取到任务统计: 总数=${result.tasks.length}`)
    }
  }

  /**
   * 查找目标项目
   * 基于用户输入和项目列表，智能匹配最相关的项目
   *
   * 匹配算法：
   * 1. 从用户输入中提取项目相关关键词
   * 2. 计算每个项目与关键词的匹配分数
   * 3. 返回分数最高且大于 0 的项目
   *
   * @param {Array} projects - 项目列表，包含项目名称、描述等信息
   * @returns {Object|null} 匹配的项目对象，未匹配到时返回 null
   *
   * 使用场景：
   * - 用户创建任务时自动选择目标项目
   * - 查询任务时确定搜索范围
   * - 提供智能的项目推荐
   */
  findTargetProject(projects) {
    const userInput = this.userInput.toLowerCase()
    const keywords = this.extractKeywords(userInput)

    // 为每个项目计算匹配分数
    const scored = projects.map((project) => ({
      project,
      score: this.calculateMatchScore(project, keywords),
    }))

    // 按分数降序排序，选择最高分的项目
    scored.sort((a, b) => b.score - a.score)
    return scored.length > 0 && scored[0].score > 0 ? scored[0].project : null
  }

  /**
   * 提取关键词
   * 从用户输入中提取项目相关的关键词
   *
   * 提取规则：
   * - 匹配 "XXX项目" 或 "XXXproject" 格式
   * - 提取其中的项目名称部分
   * - 转换为小写便于匹配
   *
   * @param {string} input - 用户输入文本
   * @returns {Array<string>} 提取的关键词数组
   *
   * 示例：
   * - "帮我在学习项目中创建任务" → ["学习"]
   * - "查看工作project的进度" → ["工作"]
   */
  extractKeywords(input) {
    const keywords = []
    // 使用正则表达式匹配项目相关的表达式
    const projectMatches = input.match(/(\w+)项目|(\w+)project/gi)
    if (projectMatches) {
      projectMatches.forEach((match) => {
        // 移除 "项目" 或 "project" 后缀，提取核心关键词
        const keyword = match.replace(/项目|project/gi, '')
        if (keyword) keywords.push(keyword.toLowerCase())
      })
    }
    return keywords
  }

  /**
   * 计算匹配分数
   * 根据项目名称与关键词的匹配程度计算分数
   *
   * 计算规则：
   * - 每个关键词在项目名称中出现一次，分数 +1
   * - 支持部分匹配（包含关系）
   * - 分数越高表示匹配度越好
   *
   * @param {Object} project - 项目对象，包含 name 属性
   * @param {Array<string>} keywords - 关键词数组
   * @returns {number} 匹配分数，0 表示无匹配
   *
   * 优化空间：
   * - 可以考虑完全匹配给更高分数
   * - 可以加入项目描述的匹配
   * - 可以考虑关键词的权重差异
   */
  calculateMatchScore(project, keywords) {
    let score = 0
    const projectName = project.name.toLowerCase()
    keywords.forEach((keyword) => {
      if (projectName.includes(keyword)) {
        score += 1 // 每个匹配的关键词贡献 1 分
      }
    })
    return score
  }
}

module.exports = {
  ExecutionContextManager,
}
