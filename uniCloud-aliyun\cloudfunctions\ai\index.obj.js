/**
 * AI 云函数主文件
 *
 * 主要功能：
 * 1. 提供与豆包 AI 的智能对话接口
 * 2. 支持流式数据传输和实时响应
 * 3. 集成意图识别和智能任务执行
 * 4. 提供完整的错误处理和异常管理
 *
 * 核心特性：
 * - SSE 流式推送：实时推送 AI 响应数据到前端
 * - 意图识别：自动识别用户意图（创建任务、查找任务、普通聊天）
 * - 智能执行：基于意图自动生成和执行任务计划
 * - 上下文管理：维护对话上下文和执行状态
 * - 性能监控：集成性能监控和统计分析
 *
 * 技术架构：
 * - 基于 uniCloud 云函数平台
 * - 使用豆包 AI（基于 OpenAI SDK）
 * - 支持 Server-Sent Events (SSE) 实时推送
 * - 模块化设计，分离配置、执行、监控等功能
 *
 * 依赖模块：
 * - OpenAI SDK：与豆包 AI 进行交互
 * - 配置模块：豆包参数、工具注册表、系统提示词
 * - 执行模块：智能计划生成、执行引擎、上下文管理
 * - 监控模块：性能监控、错误处理、统计分析
 *
 * 使用场景：
 * - 智能任务管理：创建、查询、更新任务
 * - 自然语言交互：通过对话完成复杂操作
 * - 实时协作：多用户实时交互和状态同步
 * - 数据分析：基于对话内容的智能分析和建议
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

// 导入必要的依赖模块
const OpenAI = require('openai') // OpenAI SDK，用于与豆包 AI 交互
const { doubaoParams, DEFAULT_SYSTEM_PROMPT } = require('./modules/config') // 配置参数和系统提示词
const { IntelligentExecutionPlanner } = require('./modules/planner') // 智能执行计划生成器
const { ExecutionContextManager } = require('./modules/context') // 执行上下文管理器
const { executeRobustPlan } = require('./modules/executor') // 增强执行引擎
const { globalPerformanceMonitor } = require('./modules/performance') // 全局性能监控器

/**
 * 云函数导出对象
 * 包含所有可供前端调用的方法
 *
 * 导出方法：
 * - _before: 云函数通用预处理器
 * - speak: 基础 AI 聊天对话接口（已弃用）
 * - chatStreamSSE: 流式 AI 聊天接口（主要接口）
 *
 * 设计原则：
 * - 向后兼容：保留旧接口以支持现有客户端
 * - 功能分离：不同接口承担不同的功能职责
 * - 错误隔离：每个接口独立处理错误和异常
 * - 统一格式：所有接口使用统一的返回格式
 */
module.exports = {
  /**
   * 云函数通用预处理器
   * 在每个方法执行前自动调用，用于通用的初始化操作
   *
   * 功能用途：
   * - 日志记录：记录每次云函数调用的基本信息
   * - 权限验证：验证调用者的身份和权限
   * - 参数预处理：对通用参数进行预处理和验证
   * - 环境初始化：初始化执行环境和全局变量
   *
   * 注意事项：
   * - 此方法会在每个导出方法执行前自动调用
   * - 不要在此方法中执行耗时操作，会影响整体性能
   * - 异常处理要谨慎，避免影响后续方法的执行
   */
  _before: function () {
    // 通用预处理器 - 目前为空实现
    // 可在此添加日志记录、权限验证等通用逻辑
    // 例如：console.log('云函数调用开始', new Date().toISOString())
  },

  /**
   * 基础 AI 聊天对话接口（已弃用）
   * 提供与豆包 AI 的简单对话功能，不包含意图识别和任务执行
   *
   * 弃用原因：
   * - 功能有限：不支持流式响应和实时推送
   * - 用户体验差：需要等待完整响应后才能显示结果
   * - 扩展性差：难以支持复杂的任务执行和状态管理
   *
   * 替代方案：
   * - 使用 chatStreamSSE 方法获得更好的用户体验
   * - 支持流式响应、意图识别和智能任务执行
   *
   * 保留原因：
   * - 向后兼容：支持现有的客户端调用
   * - 简单场景：适用于不需要复杂功能的简单对话
   *
   * @param {Object} params - 请求参数
   * @param {string} params.message - 用户输入的消息内容
   * @param {Array} [params.history_records] - 历史对话记录
   * @param {string} [params.model] - AI 模型名称
   * @param {string} [params.system] - 系统提示词
   * @returns {Object} 返回结果对象
   * @returns {string} [returns.errCode] - 错误码，存在时表示请求失败
   * @returns {string} [returns.errMsg] - 错误信息
   * @returns {string} [returns.content] - AI 回复内容，成功时返回
   */
  async speak(params) {
    // 解构参数并设置默认值
    const {
      message, // 用户输入的消息内容（必需参数）
      history_records = [], // 历史对话记录数组
      model = 'doubao-seed-1-6-250615', // 豆包 AI 模型名称
      system = DEFAULT_SYSTEM_PROMPT, // 系统提示词
    } = params

    // 参数验证：确保必需参数存在
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    try {
      // 初始化豆包 AI 客户端
      const openai = new OpenAI(doubaoParams)

      // 构建对话消息数组
      const messages = [{ role: 'system', content: system }, ...history_records, { role: 'user', content: message }]

      // 调用 AI 接口获取响应
      const response = await openai.chat.completions.create({
        messages,
        model,
        timeout: 300000, // 5 分钟超时
      })

      // 提取 AI 回复内容
      const content = response.choices[0]?.message?.content || ''

      // 返回成功结果
      return {
        errCode: 0,
        errMsg: 'success',
        content: content,
      }
    } catch (error) {
      // 错误处理
      console.log('AI 聊天接口错误：', error)
      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 接口失败',
      }
    }
  },

  /**
   * 流式 AI 聊天接口（主要接口）
   * 提供与豆包 AI 的智能对话功能，支持意图识别和任务执行
   *
   * 核心功能：
   * - 流式响应：实时推送 AI 生成的内容，提升用户体验
   * - 意图识别：自动识别用户意图（创建任务、查找任务、普通聊天）
   * - 智能执行：基于识别的意图自动生成和执行任务计划
   * - 上下文管理：维护对话上下文和执行状态
   * - 错误处理：完善的错误处理和异常管理机制
   *
   * 技术特性：
   * - SSE 推送：使用 Server-Sent Events 实现实时数据推送
   * - 异步处理：支持异步的 AI 调用和任务执行
   * - 状态管理：维护执行过程的状态和进度信息
   * - 性能监控：集成性能监控和统计分析
   *
   * 数据流程：
   * 1. 接收用户输入和 SSE Channel
   * 2. 调用豆包 AI 进行流式对话
   * 3. 实时解析 AI 响应，识别意图类型和内容
   * 4. 根据意图类型决定是否执行任务
   * 5. 推送处理结果和状态更新到前端
   *
   * @param {Object} params - 请求参数
   * @param {string} params.message - 用户输入的消息内容
   * @param {Array} [params.history_records] - 历史对话记录
   * @param {string} [params.model] - AI 模型名称
   * @param {string} [params.system] - 系统提示词
   * @param {Object} params.channel - SSE Channel 对象，用于实时推送
   * @returns {Object} 返回结果对象
   */
  async chatStreamSSE(params) {
    // 解构参数并设置默认值
    const {
      message, // 用户输入的消息内容（必需参数）
      history_records = [], // 历史对话记录数组
      model = 'doubao-seed-1-6-250615', // 豆包 AI 模型名称
      system = DEFAULT_SYSTEM_PROMPT, // 系统提示词
      channel, // SSE Channel 对象，用于实时推送数据到前端（必需参数）
    } = params

    // 调试日志：记录接收到的用户消息
    console.log('SSE 流式聊天消息：', message)

    // 参数验证阶段：确保必需参数存在，避免后续处理出错
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      // 反序列化 SSE Channel，将前端传递的 channel 对象转换为可用的推送通道
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 初始化豆包 AI 客户端，使用配置文件中的参数
      const openai = new OpenAI(doubaoParams)

      // 构建对话消息数组：系统提示词 + 历史记录 + 当前用户消息
      const messages = [{ role: 'system', content: system }, ...history_records]
      if (message) messages.push({ role: 'user', content: message })

      // 推送开始消息，通知前端开始处理用户请求
      await sseChannel.write({
        type: 'start',
        message: '开始生成回复...',
        timestamp: Date.now(),
      })

      // 创建流式 AI 响应，启用实时数据传输
      const streamResponse = await openai.chat.completions.create({
        messages, // 对话上下文
        model: model, // 使用的 AI 模型
        stream: true, // 启用流式响应，关键设置
        timeout: 300000, // 5 分钟超时（毫秒），防止长时间等待
      })

      // 初始化流式处理相关变量
      let fullContent = '' // 累积的完整 AI 响应内容，用于最终的意图解析
      let chunkCount = 0 // 推送的数据块计数，用于统计和调试
      let intentType = null // 识别的意图类型：task|chat
      let isChatReplyStarted = false // 是否开始推送闲聊回复的标志位
      let chatReply = '' // 提取的闲聊回复内容，仅用于 chat 类型

      // 正则表达式：匹配 AI 返回的意图类型和闲聊回复
      // task 类型只有意图类型，chat 类型有意图类型和闲聊回复
      const intentTypeRegex = /「意图类型」：(task|chat)/
      const chatReplyRegex = /「闲聊回复」：([\s\S]*)/

      // 流式处理 AI 响应数据
      for await (const chunk of streamResponse) {
        // 提取当前数据块的内容，处理可能的空值情况
        const content = chunk.choices[0]?.delta?.content || ''

        if (content) {
          fullContent += content // 累积完整内容，用于后续的正则匹配
          chunkCount++ // 增加数据块计数，用于统计和调试

          // 第一阶段：检测意图类型
          // 在累积的内容中查找意图类型标识，一旦找到就立即处理
          if (!intentType) {
            const typeMatch = intentTypeRegex.exec(fullContent)
            if (typeMatch) {
              intentType = typeMatch[1] // 提取意图类型：task|chat
              console.log(`检测到意图类型：${intentType}`)

              // 立即推送意图类型到前端，让用户知道 AI 已经理解了请求类型
              await sseChannel.write({
                type: 'intent_type',
                intentType: intentType,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送，避免重复发送相同信息
            }
          }

          // 第二阶段：根据意图类型处理内容推送
          if (intentType === 'chat') {
            // chat 类型：检测闲聊回复开始标识
            if (!isChatReplyStarted) {
              const replyMatch = chatReplyRegex.exec(fullContent)
              if (replyMatch) {
                isChatReplyStarted = true
                chatReply = replyMatch[1] // 提取已有的闲聊回复内容
                console.log('检测到闲聊回复开始')

                // 推送闲聊回复开始标识
                await sseChannel.write({
                  type: 'content_chunk',
                  content: chatReply,
                  timestamp: Date.now(),
                })
                continue // 跳过当前块的推送，避免重复发送
              }
            } else {
              // 持续推送闲聊回复内容块
              await sseChannel.write({
                type: 'content_chunk',
                content: content,
                timestamp: Date.now(),
              })
              chatReply += content // 累积闲聊回复内容
              console.log(`推送闲聊回复块：${content}`)
            }
          } else if (intentType === 'task') {
            // task 类型：不需要推送额外内容，直接进入任务执行阶段
            console.log('task 类型无需推送内容，等待任务执行')
          } else {
            // 尚未检测到意图类型，继续累积内容
            console.log(`累积内容等待意图识别：${fullContent}`)
          }
        }
      }

      // 调试输出：打印完整的处理结果，便于开发和调试
      console.log('AI 完整返回内容：', fullContent)
      console.log('提取的意图类型：', intentType)
      console.log('提取的闲聊回复：', chatReply)

      // 任务执行阶段：task 类型触发智能任务执行
      // 只有当识别到任务意图时才执行任务，chat 类型直接返回对话结果
      if (intentType === 'task') {
        // 创建执行上下文管理器，用于管理执行过程中的数据和状态
        // 传入唯一的会话 ID 和用户原始输入，便于上下文分析和数据关联
        const context = new ExecutionContextManager(IntelligentExecutionPlanner.generateUUID(), message)

        // 使用智能执行计划生成器创建执行计划
        // 基于用户输入和任务意图，AI 会生成具体的执行步骤
        const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, intentType)

        // 如果生成了有效的执行计划，则开始执行任务
        if (executionPlan.totalSteps > 0) {
          // 使用增强的执行引擎执行计划
          // 集成了错误处理、重试机制、性能监控等高级功能
          await executeRobustPlan(executionPlan, context, sseChannel, globalPerformanceMonitor)

          // 返回任务执行结果，包含详细的执行信息和性能报告
          return {
            errCode: 0,
            errMsg: 'success',
            data: {
              type: 'task_executed', // 标识这是一个任务执行结果
              intentType: intentType, // 识别的意图类型
              executionPlan: executionPlan, // 完整的执行计划信息
              contextData: Array.from(context.contextData.keys()), // 上下文数据键列表
              executionTime: executionPlan.totalExecutionTime, // 总执行时间
              performanceReport: globalPerformanceMonitor.getPerformanceReport(), // 性能报告
              content: fullContent, // AI 完整回复内容
              totalChunks: chunkCount, // 推送的数据块总数
            },
          }
        }
      }

      // 推送结束消息，标识流式聊天处理完成
      // 这是正常流程的结束，通知前端可以停止等待更多数据
      await sseChannel.end({
        type: 'end',
        content: intentType === 'chat' ? chatReply : fullContent, // chat 类型返回闲聊回复，task 类型返回完整内容
        intentType: intentType, // 识别的意图类型
        totalChunks: chunkCount, // 总共推送的数据块数量
        timestamp: Date.now(),
      })

      // 调试日志：记录流式聊天的完成情况
      console.log(`SSE 流式聊天完成，共推送${chunkCount}个数据块`)

      // 返回流式聊天完成的结果
      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'stream_complete', // 标识这是一个流式聊天完成结果
          content: intentType === 'chat' ? chatReply : fullContent,
          intentType: intentType,
          totalChunks: chunkCount,
        },
      }
    } catch (error) {
      // 错误处理：捕获并处理执行过程中的所有异常
      console.log('SSE 流式聊天错误：', error)

      // 尝试通过 SSE Channel 发送错误消息给前端
      // 这样前端可以知道发生了错误，而不是一直等待
      try {
        if (channel) {
          const sseChannel = uniCloud.deserializeSSEChannel(channel)
          await sseChannel.end({
            type: 'error',
            error: error.message || '调用 AI 流式接口失败',
            timestamp: Date.now(),
          })
        }
      } catch (channelError) {
        // 如果连错误消息都发送失败，记录日志但不抛出异常
        console.log('发送错误消息失败：', channelError)
      }

      // 返回错误结果给调用方
      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 流式接口失败',
      }
    }
  },
}
