// 工具调用模块
// 负责实际调用云函数工具和模拟工具调用

const { TOOL_REGISTRY } = require('./config')

// 真实工具调用函数
async function callRealTool(toolName, parameters) {
  const toolConfig = TOOL_REGISTRY[toolName]
  if (!toolConfig) {
    throw new Error(`未找到工具：${toolName}`)
  }

  try {
    // 调用对应的云函数
    const cloudFunction = uniCloud.importObject(toolConfig.cloudFunction)
    const result = await cloudFunction[toolConfig.method](parameters)

    return result
  } catch (error) {
    throw new Error(`工具执行失败：${error.message}`)
  }
}

// 模拟工具调用（用于测试和开发环境）
async function simulateToolCall(toolName, parameters) {
  // 模拟延迟
  await new Promise((resolve) => setTimeout(resolve, 1000))

  switch (toolName) {
    case 'getProjects':
      return {
        success: true,
        data: [
          { id: 'proj-1', name: 'OKR 项目', description: '目标管理项目' },
          { id: 'proj-2', name: '日常任务', description: '日常工作任务' },
        ],
        metadata: { total: 2, filtered: parameters.filter ? 1 : 2 },
      }

    case 'getTasks':
      return {
        success: true,
        tasks: [
          { id: 'task-1', title: '制定 Q1 目标', completed: false, projectId: parameters.projectId || 'proj-1' },
          { id: 'task-2', title: '更新 KR 进度', completed: false, projectId: parameters.projectId || 'proj-1' },
        ],
        metadata: { total: 2, projectId: parameters.projectId },
      }

    default:
      throw new Error(`未知的工具：${toolName}`)
  }
}

// 获取上下文更新信息
function getContextUpdates(context) {
  const updates = {}
  for (const [key, value] of context.contextData.entries()) {
    updates[key] = value
  }
  return updates
}

// 生成执行摘要
function generateExecutionSummary(executionPlan, context) {
  const completedSteps = executionPlan.steps.filter((s) => s.status === 'completed')
  const failedSteps = executionPlan.steps.filter((s) => s.status === 'failed')

  return {
    totalSteps: executionPlan.totalSteps,
    completedSteps: completedSteps.length,
    failedSteps: failedSteps.length,
    totalExecutionTime: executionPlan.totalExecutionTime,
    averageStepTime:
      completedSteps.length > 0
        ? Math.round(completedSteps.reduce((sum, s) => sum + s.executionTime, 0) / completedSteps.length)
        : 0,
    contextDataKeys: Array.from(context.contextData.keys()),
    success: failedSteps.length === 0,
  }
}

// 辅助函数：计算动态引用数量
function countDynamicReferences(parameters) {
  let count = 0
  const paramStr = JSON.stringify(parameters)

  // 计算各种动态引用
  count += (paramStr.match(/\$context\./g) || []).length
  count += (paramStr.match(/\$step\./g) || []).length
  count += (paramStr.match(/\$filter\(/g) || []).length

  return count
}

module.exports = {
  callRealTool,
  simulateToolCall,
  getContextUpdates,
  generateExecutionSummary,
  countDynamicReferences,
}
