/**
 * AI 助手 V1.0 工具注册系统集成测试
 * 测试 chatStreamSSE 函数与工具注册系统的集成
 */

import { describe, it, expect, beforeEach } from '@jest/globals'

// 模拟 AI 助手模块的核心功能
const TOOL_REGISTRY = {
  getProjects: {
    name: 'getProjects',
    description: '获取滴答清单中的所有项目',
    usage: '当用户想要查看、搜索项目时使用',
    parameters: {
      filter: {
        type: 'string',
        required: false,
        default: '',
        description: '项目名称筛选关键词'
      }
    },
    metadata: {
      priority: 0.8,
      category: 'data_retrieval'
    }
  },
  getTasks: {
    name: 'getTasks',
    description: '获取指定项目下的任务列表',
    usage: '当用户想要查看、搜索、查询任务时使用',
    parameters: {
      projectId: {
        type: 'string',
        required: true,
        description: '项目 ID，必须是有效的项目标识符'
      }
    },
    metadata: {
      priority: 0.9,
      category: 'data_retrieval'
    }
  }
}

function generateToolPrompt(toolRegistry) {
  let toolPrompt = '你可以使用以下工具来帮助用户完成任务：\n\n'

  for (const [toolKey, tool] of Object.entries(toolRegistry)) {
    toolPrompt += `**${tool.name}** (${toolKey})\n`
    toolPrompt += `- 功能：${tool.description}\n`
    toolPrompt += `- 使用场景：${tool.usage}\n`
    toolPrompt += `- 优先级：${tool.metadata?.priority || 'N/A'}\n`
    toolPrompt += `- 参数：\n`

    if (tool.parameters) {
      for (const [paramName, param] of Object.entries(tool.parameters)) {
        const required = param.required ? '必需' : '可选'
        const defaultValue = param.default ? ` (默认: ${param.default})` : ''
        toolPrompt += `  - ${paramName} (${param.type}, ${required}${defaultValue}): ${param.description}\n`
      }
    }
    
    toolPrompt += '\n'
  }

  return toolPrompt
}

// 模拟 chatStreamSSE 函数的核心逻辑
async function mockChatStreamSSE(params) {
  const toolPrompt = generateToolPrompt(TOOL_REGISTRY)
  
  let {
    message,
    messages: history_records = [],
    model = 'doubao-seed-1-6-250615',
    system = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

${toolPrompt}

请分析用户输入内容，并将其归类为以下两种意图之一：
1. task: 当用户想要执行任何与任务相关的操作时（包括创建、查询、修改、删除任务等）
2. chat: 其他所有不属于任务操作的内容，视为一般闲聊对话

分析完成后，必须严格按照以下格式输出结果：
- 如果是 task 类型，只输出：「意图类型」：task
- 如果是 chat 类型，输出：
  「意图类型」：chat
  「闲聊回复」：[针对用户问题的回复内容]

注意：
- 分析要准确，不要混淆不同意图类型
- 只输出上述指定的两行内容，不要添加任何其他解释或内容
- 确保格式严格遵循示例，包括使用中文引号「」
- 虽然现在有可用的工具，但在 V1.0 版本中暂不执行工具调用，仅进行意图识别。`,
    channel
  } = params

  // 参数校验
  if (!message) {
    return {
      errCode: 'PARAM_IS_NULL',
      errMsg: '消息内容不能为空'
    }
  }

  if (!channel) {
    return {
      errCode: 'PARAM_IS_NULL',
      errMsg: 'SSE Channel 不能为空'
    }
  }

  // 验证系统提示词包含工具信息
  const hasToolInfo = system.includes('getProjects') && system.includes('getTasks')
  
  return {
    errCode: 0,
    errMsg: 'success',
    data: {
      type: 'stream_complete',
      content: '模拟 AI 回复',
      intentType: 'find_task',
      totalChunks: 1,
      systemPromptContainsTools: hasToolInfo,
      toolPromptGenerated: toolPrompt.length > 0
    }
  }
}

describe('AI 助手 V1.0 工具注册系统集成测试', () => {

  describe('chatStreamSSE 集成测试', () => {
    it('应该成功处理带有工具信息的请求', async () => {
      const params = {
        message: '查看我的项目列表',
        channel: 'mock-channel'
      }

      const result = await mockChatStreamSSE(params)
      
      expect(result.errCode).toBe(0)
      expect(result.data.systemPromptContainsTools).toBe(true)
      expect(result.data.toolPromptGenerated).toBe(true)
    })

    it('应该在系统提示词中包含工具描述', async () => {
      const params = {
        message: '帮我创建一个任务',
        channel: 'mock-channel'
      }

      const result = await mockChatStreamSSE(params)
      
      expect(result.errCode).toBe(0)
      expect(result.data.systemPromptContainsTools).toBe(true)
    })

    it('应该正确处理参数验证', async () => {
      // 测试缺少 message 参数
      const paramsWithoutMessage = {
        channel: 'mock-channel'
      }

      const result1 = await mockChatStreamSSE(paramsWithoutMessage)
      expect(result1.errCode).toBe('PARAM_IS_NULL')
      expect(result1.errMsg).toContain('消息内容不能为空')

      // 测试缺少 channel 参数
      const paramsWithoutChannel = {
        message: '测试消息'
      }

      const result2 = await mockChatStreamSSE(paramsWithoutChannel)
      expect(result2.errCode).toBe('PARAM_IS_NULL')
      expect(result2.errMsg).toContain('SSE Channel 不能为空')
    })

    it('应该生成包含所有工具信息的提示词', async () => {
      const toolPrompt = generateToolPrompt(TOOL_REGISTRY)
      
      // 验证包含工具名称
      expect(toolPrompt).toContain('getProjects')
      expect(toolPrompt).toContain('getTasks')
      
      // 验证包含工具描述
      expect(toolPrompt).toContain('获取滴答清单中的所有项目')
      expect(toolPrompt).toContain('获取指定项目下的任务列表')
      
      // 验证包含优先级信息
      expect(toolPrompt).toContain('优先级：0.8')
      expect(toolPrompt).toContain('优先级：0.9')
      
      // 验证包含参数信息
      expect(toolPrompt).toContain('filter (string, 可选)')
      expect(toolPrompt).toContain('projectId (string, 必需)')
    })

    it('应该保持 V1.0 版本的功能边界', async () => {
      const params = {
        message: '查看 OKR 项目的任务',
        channel: 'mock-channel'
      }

      const result = await mockChatStreamSSE(params)
      
      // V1.0 版本应该只进行意图识别，不执行工具调用
      expect(result.errCode).toBe(0)
      expect(result.data.type).toBe('stream_complete')
      
      // 确认系统提示词包含 V1.0 版本的说明
      const toolPrompt = generateToolPrompt(TOOL_REGISTRY)
      const systemPrompt = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

${toolPrompt}

请分析用户输入内容，并将其归类为以下三种意图之一：
1. create_task: 当用户想要创建、添加、设置、安排一个新任务时
2. find_task: 当用户想要查询、搜索、查看已有的任务时
3. chat: 其他所有不属于创建任务或查找任务的内容，视为一般闲聊

分析完成后，必须严格按照以下格式输出结果：
「意图类型」：[意图类型代码，必须是 create_task、find_task 或 chat 之一]
「意图内容」：[如果是创建任务，提取出要创建的任务内容；如果是查找任务，提取出要查找的任务关键词；如果是闲聊，则回复用户问题]

注意：
- 分析要准确，不要混淆不同意图类型
- 只输出上述指定的两行内容，不要添加任何其他解释或内容
- 确保格式严格遵循示例，包括使用中文引号「」
- 虽然现在有可用的工具，但在 V1.0 版本中暂不执行工具调用，仅进行意图识别。`
      
      expect(systemPrompt).toContain('虽然现在有可用的工具，但在 V1.0 版本中暂不执行工具调用，仅进行意图识别')
    })
  })
})
分析完成后，必须严格按照以下格式输出结果：
「意图类型」：[意图类型代码，必须是 create_task、find_task 或 chat 之一]
「意图内容」：[如果是创建任务，提取出要创建的任务内容；如果是查找任务，提取出要查找的任务关键词；如果是闲聊，则回复用户问题]

注意：
- 分析要准确，不要混淆不同意图类型
- 只输出上述指定的两行内容，不要添加任何其他解释或内容
- 确保格式严格遵循示例，包括使用中文引号「」
- 虽然现在有可用的工具，但在 V1.0 版本中暂不执行工具调用，仅进行意图识别。`
      
      expect(systemPrompt).toContain('虽然现在有可用的工具，但在 V1.0 版本中暂不执行工具调用，仅进行意图识别')
    })
  })
})
      const result = ParameterValidator.validate('getTasks', validParams)
      expect(result.projectId).toBe('test-123')
      expect(result.completed).toBe(false)
      expect(result.limit).toBe(25)
    })

    it('应该检测缺少必需参数', () => {
      const invalidParams = { completed: false }
      expect(() => ParameterValidator.validate('getTasks', invalidParams))
        .toThrow('缺少必需参数：projectId')
    })

    it('应该应用默认值', () => {
      const params = { projectId: 'test-123' }
      const result = ParameterValidator.validate('getTasks', params)
      expect(result.completed).toBe(false) // 默认值
      expect(result.limit).toBe(50) // 默认值
    })

    it('应该验证参数类型', () => {
      const invalidParams = { projectId: 123, completed: false }
      expect(() => ParameterValidator.validate('getTasks', invalidParams))
        .toThrow('参数 projectId 应为字符串类型，实际为 number')
    })

    it('应该验证数值范围', () => {
      const invalidParams = { projectId: 'test-123', limit: 150 }
      expect(() => ParameterValidator.validate('getTasks', invalidParams))
        .toThrow('参数 limit 不能大于 100')
    })

    it('应该验证字符串格式', () => {
      const invalidParams = { projectId: 'test@123!' }
      expect(() => ParameterValidator.validate('getTasks', invalidParams))
        .toThrow('参数 projectId 格式不符合要求')
    })

    it('应该处理不存在的工具', () => {
      expect(() => ParameterValidator.validate('nonExistentTool', {}))
        .toThrow('未找到工具：nonExistentTool')
    })
  })

  describe('动态提示词生成器', () => {
    it('应该生成包含工具信息的提示词', () => {
      const prompt = generateToolPrompt(TOOL_REGISTRY)
      expect(prompt).toContain('getProjects')
      expect(prompt).toContain('getTasks')
      expect(prompt).toContain('功能：')
      expect(prompt).toContain('参数：')
    })

    it('应该包含工具的详细信息', () => {
      const prompt = generateToolPrompt(TOOL_REGISTRY)
      expect(prompt).toContain('获取滴答清单中的所有项目')
      expect(prompt).toContain('获取指定项目下的任务列表')
      expect(prompt).toContain('优先级：0.8')
      expect(prompt).toContain('优先级：0.9')
    })

    it('应该包含参数信息', () => {
      const prompt = generateToolPrompt(TOOL_REGISTRY)
      expect(prompt).toContain('projectId (string, 必需)')
      expect(prompt).toContain('completed (boolean, 可选)')
      expect(prompt).toContain('limit (number, 可选 (默认: 50))')
    })
  })
})
