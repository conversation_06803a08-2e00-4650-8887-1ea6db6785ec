/**
 * dida-analytics 云函数
 * 
 * 功能概述：
 * 提供滴答清单数据的深度分析和智能洞察功能，包括：
 * - 目标统计分析：目标完成情况、进度追踪、趋势分析
 * - 任务统计分析：任务完成率、关键词提取、效率分析
 * - 预测分析：目标完成预测、趋势预测、风险评估
 * - 报告生成：目标报告、周总结、数据可视化
 * - 性能优化：数据缓存、批量处理、智能算法
 * 
 * 技术架构：
 * - 基于 dida-base 云函数的统一 API 封装
 * - 支持多维度数据分析和统计
 * - 实现智能预测算法和机器学习模型
 * - 提供丰富的数据可视化和报告功能
 * - 优化的缓存机制和性能调优
 * 
 * 数据来源：
 * - 任务数据：通过 dida-base 获取任务列表和详情
 * - 项目数据：通过 dida-base 获取项目信息
 * - 目标数据：通过 dida-goal 获取目标信息
 * - 标签数据：通过 dida-tag 获取标签信息
 * - 历史数据：通过时间序列分析获取趋势数据
 * 
 * 分析维度：
 * - 时间维度：日、周、月、季度、年度分析
 * - 类型维度：任务类型、项目类型、目标类型分析
 * - 状态维度：完成状态、进度状态、优先级分析
 * - 关联维度：任务-目标关联、项目-目标关联分析
 * - 效率维度：完成效率、时间分布、工作负载分析
 * 
 * <AUTHOR> 开发团队
 * @version 1.0.0
 * @since 2024-01-01
 */

module.exports = {
  // ==================== 基础设施方法 ====================

  /**
   * 获取 dida-base 云函数实例
   *
   * @returns {object} dida-base 云函数实例
   */
  _getBaseApi: function () {
    return uniCloud.importObject('dida-base')
  },

  /**
   * 获取缓存键名
   * 
   * @param {string} type 缓存类型
   * @param {string} key 缓存键
   * @returns {string} 完整的缓存键名
   */
  _getCacheKey: function (type, key) {
    return `dida_analytics_${type}_${key}`
  },

  /**
   * 获取缓存数据
   * 
   * @param {string} cacheKey 缓存键
   * @param {number} expireMinutes 过期时间（分钟）
   * @returns {object|null} 缓存数据或null
   */
  _getCache: async function (cacheKey, expireMinutes = 30) {
    try {
      const db = uniCloud.database()
      const collection = db.collection('dida_analytics_cache')
      
      const result = await collection
        .where({
          key: cacheKey,
          expireTime: db.command.gt(new Date())
        })
        .orderBy('createTime', 'desc')
        .limit(1)
        .get()

      if (result.data && result.data.length > 0) {
        return JSON.parse(result.data[0].data)
      }
      
      return null
    } catch (error) {
      console.warn('获取缓存失败：', error)
      return null
    }
  },

  /**
   * 设置缓存数据
   * 
   * @param {string} cacheKey 缓存键
   * @param {object} data 要缓存的数据
   * @param {number} expireMinutes 过期时间（分钟）
   */
  _setCache: async function (cacheKey, data, expireMinutes = 30) {
    try {
      const db = uniCloud.database()
      const collection = db.collection('dida_analytics_cache')
      
      const expireTime = new Date(Date.now() + expireMinutes * 60 * 1000)
      
      await collection.add({
        key: cacheKey,
        data: JSON.stringify(data),
        createTime: new Date(),
        expireTime: expireTime
      })
    } catch (error) {
      console.warn('设置缓存失败：', error)
    }
  },

  /**
   * 获取所有任务数据
   * 
   * @returns {array} 任务数组
   */
  _getAllTasks: async function () {
    const baseApi = this._getBaseApi()
    const response = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
    
    if (!response.success) {
      throw new Error('获取任务数据失败')
    }
    
    return response.data.syncTaskBean?.update || []
  },

  /**
   * 获取所有项目数据
   * 
   * @returns {array} 项目数组
   */
  _getAllProjects: async function () {
    const baseApi = this._getBaseApi()
    const response = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
    
    if (!response.success) {
      throw new Error('获取项目数据失败')
    }
    
    return response.data.projectProfiles || []
  },

  /**
   * 获取所有目标数据
   * 
   * @returns {array} 目标数组
   */
  _getAllGoals: async function () {
    try {
      const didaGoal = uniCloud.importObject('dida-goal')
      const response = await didaGoal.getGoals()
      
      if (!response.success) {
        throw new Error('获取目标数据失败')
      }
      
      return response.data || []
    } catch (error) {
      console.warn('获取目标数据失败，返回空数组：', error)
      return []
    }
  },

  /**
   * 计算日期差（天数）
   * 
   * @param {string|Date} startDate 开始日期
   * @param {string|Date} endDate 结束日期
   * @returns {number} 天数差
   */
  _calculateDaysDiff: function (startDate, endDate) {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end - start)
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  },

  /**
   * 格式化日期为 YYYY-MM-DD
   * 
   * @param {string|Date} date 日期
   * @returns {string} 格式化的日期字符串
   */
  _formatDate: function (date) {
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  /**
   * 获取时间范围内的日期数组
   * 
   * @param {string|Date} startDate 开始日期
   * @param {string|Date} endDate 结束日期
   * @returns {array} 日期字符串数组
   */
  _getDateRange: function (startDate, endDate) {
    const dates = []
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      dates.push(this._formatDate(d))
    }
    
    return dates
  },

  // ==================== 目标统计分析功能 ====================

  /**
   * 获取目标统计信息
   * 
   * 功能说明：
   * - 统计目标总数、完成数、进行中数量
   * - 按类型分析目标分布
   * - 计算目标完成率和平均进度
   * - 分析目标时间分布
   * 
   * @param {object} params 参数对象
   * @param {boolean} [params.force_refresh] 是否强制刷新缓存数据
   * @returns {object} 目标统计数据
   */
  getGoalStatistics: async function (params) {
    console.log('--- 调用 getGoalStatistics ---', params)
    const { force_refresh = false } = params || {}

    try {
      const baseApi = this._getBaseApi()
      const cacheKey = this._getCacheKey('goal_statistics', 'all')

      // 检查缓存
      if (!force_refresh) {
        const cachedData = await this._getCache(cacheKey, 60) // 缓存1小时
        if (cachedData) {
          console.log('getGoalStatistics 使用缓存数据')
          return baseApi.formatResponse(cachedData)
        }
      }

      // 获取目标数据
      const goals = await this._getAllGoals()

      // 基础统计
      const totalGoals = goals.length
      const completedGoals = goals.filter(goal => goal.status === 'completed').length
      const activeGoals = goals.filter(goal => goal.status === 'active').length
      const completionRate = totalGoals > 0 ? (completedGoals / totalGoals * 100).toFixed(1) : 0

      // 按类型统计
      const typeStats = {
        phase: goals.filter(goal => goal.type === 'phase').length,
        permanent: goals.filter(goal => goal.type === 'permanent').length,
        habit: goals.filter(goal => goal.type === 'habit').length
      }

      // 平均进度计算
      const totalProgress = goals.reduce((sum, goal) => sum + (goal.progress || 0), 0)
      const averageProgress = totalGoals > 0 ? (totalProgress / totalGoals).toFixed(1) : 0

      // 时间分析
      const now = new Date()
      const thisMonth = goals.filter(goal => {
        if (!goal.createdTime) return false
        const created = new Date(goal.createdTime)
        return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear()
      }).length

      const statistics = {
        overview: {
          totalGoals,
          completedGoals,
          activeGoals,
          completionRate: parseFloat(completionRate),
          averageProgress: parseFloat(averageProgress)
        },
        typeDistribution: typeStats,
        timeAnalysis: {
          createdThisMonth: thisMonth,
          completedThisMonth: goals.filter(goal => {
            if (goal.status !== 'completed' || !goal.modifiedTime) return false
            const modified = new Date(goal.modifiedTime)
            return modified.getMonth() === now.getMonth() && modified.getFullYear() === now.getFullYear()
          }).length
        },
        trends: {
          dailyAverage: totalGoals > 0 ? (totalGoals / 30).toFixed(1) : 0, // 假设30天
          weeklyGrowth: 0 // 需要历史数据计算
        }
      }

      // 缓存结果
      await this._setCache(cacheKey, statistics, 60)

      console.log('getGoalStatistics 成功，目标总数：', totalGoals)
      return baseApi.formatResponse(statistics)
    } catch (error) {
      console.error('getGoalStatistics 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GET_GOAL_STATISTICS_FAILED', '获取目标统计失败')
    }
  },

  /**
   * 获取目标进度历史
   *
   * 功能说明：
   * - 获取指定目标的进度变化历史
   * - 分析进度趋势和变化模式
   * - 计算进度速度和预期完成时间
   *
   * @param {object} params 参数对象
   * @param {string} params.goal_id 目标ID（必需）
   * @returns {object} 目标进度历史数据
   */
  getGoalProgress: async function (params) {
    console.log('--- 调用 getGoalProgress ---', params)
    const { goal_id } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!goal_id) {
        return baseApi.handleError(new Error('目标ID不能为空'), 'GOAL_ID_REQUIRED', '目标ID不能为空')
      }

      // 获取目标详情
      const didaGoal = uniCloud.importObject('dida-goal')
      const goalResponse = await didaGoal.getGoal({ goal_id })

      if (!goalResponse.success) {
        return goalResponse
      }

      const goal = goalResponse.data

      // 模拟进度历史数据（实际应用中需要从数据库获取历史记录）
      const progressHistory = this._generateProgressHistory(goal)

      // 计算进度趋势
      const trend = this._calculateProgressTrend(progressHistory)

      // 预测完成时间
      const prediction = this._predictCompletionTime(goal, progressHistory, trend)

      const result = {
        goalInfo: {
          id: goal.id,
          title: goal.title,
          type: goal.type,
          currentProgress: goal.progress || 0,
          status: goal.status
        },
        progressHistory,
        trend: {
          direction: trend.direction, // 'increasing', 'decreasing', 'stable'
          speed: trend.speed, // 每日平均进度增长
          consistency: trend.consistency // 进度一致性评分
        },
        prediction: {
          estimatedCompletionDate: prediction.completionDate,
          daysRemaining: prediction.daysRemaining,
          confidence: prediction.confidence // 预测置信度
        },
        insights: this._generateProgressInsights(goal, progressHistory, trend)
      }

      console.log('getGoalProgress 成功，目标ID：', goal_id)
      return baseApi.formatResponse(result)
    } catch (error) {
      console.error('getGoalProgress 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GET_GOAL_PROGRESS_FAILED', '获取目标进度失败')
    }
  },

  /**
   * 生成模拟进度历史数据
   *
   * @param {object} goal 目标对象
   * @returns {array} 进度历史数组
   */
  _generateProgressHistory: function (goal) {
    const history = []
    const startDate = new Date(goal.createdTime || Date.now() - 30 * 24 * 60 * 60 * 1000)
    const currentProgress = goal.progress || 0
    const days = Math.min(30, this._calculateDaysDiff(startDate, new Date()))

    for (let i = 0; i <= days; i++) {
      const date = new Date(startDate)
      date.setDate(date.getDate() + i)

      // 模拟进度增长（实际应用中应从数据库获取）
      const progress = Math.min(currentProgress, Math.floor((currentProgress / days) * i + Math.random() * 5))

      history.push({
        date: this._formatDate(date),
        progress: Math.max(0, progress),
        milestone: i % 7 === 0 ? `第${Math.floor(i/7)+1}周` : null
      })
    }

    return history
  },

  /**
   * 计算进度趋势
   *
   * @param {array} progressHistory 进度历史数组
   * @returns {object} 趋势分析结果
   */
  _calculateProgressTrend: function (progressHistory) {
    if (progressHistory.length < 2) {
      return { direction: 'stable', speed: 0, consistency: 0 }
    }

    const recent = progressHistory.slice(-7) // 最近7天
    const changes = []

    for (let i = 1; i < recent.length; i++) {
      changes.push(recent[i].progress - recent[i-1].progress)
    }

    const avgChange = changes.reduce((sum, change) => sum + change, 0) / changes.length
    const variance = changes.reduce((sum, change) => sum + Math.pow(change - avgChange, 2), 0) / changes.length
    const consistency = Math.max(0, 100 - variance * 10) // 一致性评分

    let direction = 'stable'
    if (avgChange > 0.5) direction = 'increasing'
    else if (avgChange < -0.5) direction = 'decreasing'

    return {
      direction,
      speed: Math.abs(avgChange),
      consistency: Math.round(consistency)
    }
  },

  /**
   * 预测完成时间
   *
   * @param {object} goal 目标对象
   * @param {array} progressHistory 进度历史
   * @param {object} trend 趋势分析
   * @returns {object} 预测结果
   */
  _predictCompletionTime: function (goal, progressHistory, trend) {
    const currentProgress = goal.progress || 0
    const remainingProgress = 100 - currentProgress

    if (remainingProgress <= 0) {
      return {
        completionDate: this._formatDate(new Date()),
        daysRemaining: 0,
        confidence: 100
      }
    }

    if (trend.speed <= 0) {
      return {
        completionDate: null,
        daysRemaining: null,
        confidence: 0
      }
    }

    const estimatedDays = Math.ceil(remainingProgress / trend.speed)
    const completionDate = new Date()
    completionDate.setDate(completionDate.getDate() + estimatedDays)

    // 置信度基于趋势一致性和进度速度
    const confidence = Math.min(100, trend.consistency * 0.7 + (trend.speed > 1 ? 30 : 20))

    return {
      completionDate: this._formatDate(completionDate),
      daysRemaining: estimatedDays,
      confidence: Math.round(confidence)
    }
  },

  /**
   * 生成进度洞察
   *
   * @param {object} goal 目标对象
   * @param {array} progressHistory 进度历史
   * @param {object} trend 趋势分析
   * @returns {array} 洞察数组
   */
  _generateProgressInsights: function (goal, progressHistory, trend) {
    const insights = []

    // 进度趋势洞察
    if (trend.direction === 'increasing') {
      insights.push({
        type: 'positive',
        message: `目标进展良好，保持当前速度可按时完成`,
        priority: 'low'
      })
    } else if (trend.direction === 'decreasing') {
      insights.push({
        type: 'warning',
        message: `目标进度有所放缓，建议调整执行策略`,
        priority: 'medium'
      })
    } else {
      insights.push({
        type: 'info',
        message: `目标进度相对稳定，可考虑加快推进速度`,
        priority: 'low'
      })
    }

    // 一致性洞察
    if (trend.consistency < 50) {
      insights.push({
        type: 'warning',
        message: `进度波动较大，建议制定更稳定的执行计划`,
        priority: 'medium'
      })
    }

    // 类型特定洞察
    if (goal.type === 'habit' && trend.speed < 1) {
      insights.push({
        type: 'suggestion',
        message: `习惯目标需要持续坚持，建议设置每日提醒`,
        priority: 'high'
      })
    }

    return insights
  },

  // ==================== 任务统计分析功能 ====================

  /**
   * 获取任务统计信息
   *
   * 功能说明：
   * - 统计指定时间范围内的任务数据
   * - 分析任务完成率和效率
   * - 按优先级、项目、标签分析任务分布
   * - 计算任务处理时间和工作负载
   *
   * @param {object} params 参数对象
   * @param {number} [params.days] 统计的天数范围（默认30天）
   * @param {boolean} [params.force_refresh] 是否强制刷新缓存数据
   * @returns {object} 任务统计数据
   */
  getTaskStatistics: async function (params) {
    console.log('--- 调用 getTaskStatistics ---', params)
    const { days = 30, force_refresh = false } = params || {}

    try {
      const baseApi = this._getBaseApi()
      const cacheKey = this._getCacheKey('task_statistics', `${days}days`)

      // 检查缓存
      if (!force_refresh) {
        const cachedData = await this._getCache(cacheKey, 30) // 缓存30分钟
        if (cachedData) {
          console.log('getTaskStatistics 使用缓存数据')
          return baseApi.formatResponse(cachedData)
        }
      }

      // 获取任务数据
      const allTasks = await this._getAllTasks()
      const projects = await this._getAllProjects()

      // 时间范围过滤
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)

      const recentTasks = allTasks.filter(task => {
        if (!task.modifiedTime) return false
        return new Date(task.modifiedTime) >= cutoffDate
      })

      // 基础统计
      const totalTasks = recentTasks.length
      const completedTasks = recentTasks.filter(task => task.status === 2).length
      const pendingTasks = recentTasks.filter(task => task.status === 0).length
      const completionRate = totalTasks > 0 ? (completedTasks / totalTasks * 100).toFixed(1) : 0

      // 优先级分析
      const priorityStats = {
        high: recentTasks.filter(task => task.priority === 5).length,
        medium: recentTasks.filter(task => task.priority === 3).length,
        low: recentTasks.filter(task => task.priority === 1).length,
        none: recentTasks.filter(task => !task.priority || task.priority === 0).length
      }

      // 项目分析
      const projectStats = {}
      projects.forEach(project => {
        const projectTasks = recentTasks.filter(task => task.projectId === project.id)
        if (projectTasks.length > 0) {
          projectStats[project.name] = {
            total: projectTasks.length,
            completed: projectTasks.filter(task => task.status === 2).length,
            completionRate: projectTasks.length > 0 ?
              (projectTasks.filter(task => task.status === 2).length / projectTasks.length * 100).toFixed(1) : 0
          }
        }
      })

      // 时间分析
      const dailyStats = this._calculateDailyTaskStats(recentTasks, days)

      const statistics = {
        overview: {
          totalTasks,
          completedTasks,
          pendingTasks,
          completionRate: parseFloat(completionRate),
          averagePerDay: (totalTasks / days).toFixed(1)
        },
        priorityDistribution: priorityStats,
        projectAnalysis: projectStats,
        timeAnalysis: {
          dailyStats,
          peakDay: this._findPeakDay(dailyStats),
          averageCompletionTime: this._calculateAverageCompletionTime(recentTasks)
        },
        trends: {
          weeklyGrowth: this._calculateWeeklyGrowth(dailyStats),
          efficiency: this._calculateEfficiency(recentTasks)
        }
      }

      // 缓存结果
      await this._setCache(cacheKey, statistics, 30)

      console.log('getTaskStatistics 成功，任务总数：', totalTasks)
      return baseApi.formatResponse(statistics)
    } catch (error) {
      console.error('getTaskStatistics 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GET_TASK_STATISTICS_FAILED', '获取任务统计失败')
    }
  },

  /**
   * 计算每日任务统计
   *
   * @param {array} tasks 任务数组
   * @param {number} days 天数
   * @returns {array} 每日统计数组
   */
  _calculateDailyTaskStats: function (tasks, days) {
    const dailyStats = []
    const endDate = new Date()

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(endDate)
      date.setDate(date.getDate() - i)
      const dateStr = this._formatDate(date)

      const dayTasks = tasks.filter(task => {
        if (!task.modifiedTime) return false
        return this._formatDate(task.modifiedTime) === dateStr
      })

      dailyStats.push({
        date: dateStr,
        total: dayTasks.length,
        completed: dayTasks.filter(task => task.status === 2).length,
        created: dayTasks.filter(task => {
          if (!task.createdTime) return false
          return this._formatDate(task.createdTime) === dateStr
        }).length
      })
    }

    return dailyStats
  },

  /**
   * 找出任务最多的一天
   *
   * @param {array} dailyStats 每日统计数组
   * @returns {object} 峰值日信息
   */
  _findPeakDay: function (dailyStats) {
    if (dailyStats.length === 0) return null

    const peakDay = dailyStats.reduce((max, day) =>
      day.total > max.total ? day : max
    )

    return {
      date: peakDay.date,
      taskCount: peakDay.total,
      completedCount: peakDay.completed
    }
  },

  /**
   * 计算平均完成时间
   *
   * @param {array} tasks 任务数组
   * @returns {number} 平均完成时间（小时）
   */
  _calculateAverageCompletionTime: function (tasks) {
    const completedTasks = tasks.filter(task =>
      task.status === 2 && task.createdTime && task.modifiedTime
    )

    if (completedTasks.length === 0) return 0

    const totalHours = completedTasks.reduce((sum, task) => {
      const created = new Date(task.createdTime)
      const completed = new Date(task.modifiedTime)
      const hours = (completed - created) / (1000 * 60 * 60)
      return sum + hours
    }, 0)

    return (totalHours / completedTasks.length).toFixed(1)
  },

  /**
   * 计算周增长率
   *
   * @param {array} dailyStats 每日统计数组
   * @returns {number} 周增长率百分比
   */
  _calculateWeeklyGrowth: function (dailyStats) {
    if (dailyStats.length < 14) return 0

    const thisWeek = dailyStats.slice(-7).reduce((sum, day) => sum + day.total, 0)
    const lastWeek = dailyStats.slice(-14, -7).reduce((sum, day) => sum + day.total, 0)

    if (lastWeek === 0) return 0

    return ((thisWeek - lastWeek) / lastWeek * 100).toFixed(1)
  },

  /**
   * 计算效率指标
   *
   * @param {array} tasks 任务数组
   * @returns {object} 效率指标
   */
  _calculateEfficiency: function (tasks) {
    const completedTasks = tasks.filter(task => task.status === 2)
    const totalTasks = tasks.length

    if (totalTasks === 0) {
      return { score: 0, level: 'low' }
    }

    const completionRate = completedTasks.length / totalTasks
    const highPriorityCompleted = completedTasks.filter(task => task.priority >= 3).length
    const highPriorityTotal = tasks.filter(task => task.priority >= 3).length

    let score = completionRate * 70 // 基础完成率权重70%

    if (highPriorityTotal > 0) {
      score += (highPriorityCompleted / highPriorityTotal) * 30 // 高优先级完成率权重30%
    }

    score = Math.round(score)

    let level = 'low'
    if (score >= 80) level = 'high'
    else if (score >= 60) level = 'medium'

    return { score, level }
  },

  /**
   * 从任务中提取关键词
   *
   * 功能说明：
   * - 分析任务标题和内容，提取高频关键词
   * - 统计关键词出现频率
   * - 识别任务主题和模式
   *
   * @param {object} params 参数对象
   * @param {number} [params.limit] 返回的关键词数量（默认20）
   * @param {boolean} [params.force_refresh] 是否强制刷新缓存数据
   * @returns {object} 关键词频率字典
   */
  extractTaskKeywords: async function (params) {
    console.log('--- 调用 extractTaskKeywords ---', params)
    const { limit = 20, force_refresh = false } = params || {}

    try {
      const baseApi = this._getBaseApi()
      const cacheKey = this._getCacheKey('task_keywords', `limit_${limit}`)

      // 检查缓存
      if (!force_refresh) {
        const cachedData = await this._getCache(cacheKey, 120) // 缓存2小时
        if (cachedData) {
          console.log('extractTaskKeywords 使用缓存数据')
          return baseApi.formatResponse(cachedData)
        }
      }

      // 获取任务数据
      const allTasks = await this._getAllTasks()

      // 提取文本内容
      const textContent = allTasks.map(task => {
        const title = task.title || ''
        const content = task.content || ''
        return (title + ' ' + content).toLowerCase()
      }).join(' ')

      // 关键词提取和统计
      const keywords = this._extractKeywords(textContent)
      const sortedKeywords = Object.entries(keywords)
        .sort(([,a], [,b]) => b - a)
        .slice(0, limit)
        .reduce((obj, [word, count]) => {
          obj[word] = count
          return obj
        }, {})

      // 分析关键词类别
      const categories = this._categorizeKeywords(sortedKeywords)

      const result = {
        keywords: sortedKeywords,
        categories,
        totalWords: Object.keys(keywords).length,
        totalTasks: allTasks.length,
        insights: this._generateKeywordInsights(sortedKeywords, categories)
      }

      // 缓存结果
      await this._setCache(cacheKey, result, 120)

      console.log('extractTaskKeywords 成功，关键词数量：', Object.keys(sortedKeywords).length)
      return baseApi.formatResponse(result)
    } catch (error) {
      console.error('extractTaskKeywords 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'EXTRACT_TASK_KEYWORDS_FAILED', '提取任务关键词失败')
    }
  },

  /**
   * 提取关键词
   *
   * @param {string} text 文本内容
   * @returns {object} 关键词频率对象
   */
  _extractKeywords: function (text) {
    // 停用词列表
    const stopWords = new Set([
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '它', '他', '她', '们', '这个', '那个', '什么', '怎么', '为什么', '哪里', '谁', '多少', '几', '第', '个', '些', '每', '各', '另', '别', '其他', '等', '等等', '如果', '但是', '然后', '所以', '因为', '虽然', '可是', '不过', '而且', '或者', '还是', '以及', '以', '及', '与', '同', '对', '向', '从', '把', '被', '让', '使', '给', '为', '按', '照', '根据', '通过', '经过', '关于', '由于', '除了', '包括', '含', '含有', '包含', '具有', '拥有', '存在', '发生', '出现', '产生', '形成', '建立', '创建', '制作', '完成', '实现', '达到', '获得', '取得', '得到', '收到', '接受', '接收', '提供', '给予', '送', '交', '传', '递', '送给', '交给', '传给', '递给'
    ])

    // 分词（简单的基于空格和标点符号）
    const words = text
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ') // 保留中文、英文、数字
      .split(/\s+/)
      .filter(word => word.length >= 2 && !stopWords.has(word))

    // 统计词频
    const wordCount = {}
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1
    })

    // 过滤低频词
    const filteredWords = {}
    Object.entries(wordCount).forEach(([word, count]) => {
      if (count >= 2) { // 至少出现2次
        filteredWords[word] = count
      }
    })

    return filteredWords
  },

  /**
   * 关键词分类
   *
   * @param {object} keywords 关键词对象
   * @returns {object} 分类结果
   */
  _categorizeKeywords: function (keywords) {
    const categories = {
      work: [], // 工作相关
      study: [], // 学习相关
      life: [], // 生活相关
      health: [], // 健康相关
      technology: [], // 技术相关
      other: [] // 其他
    }

    const categoryPatterns = {
      work: ['工作', '项目', '会议', '报告', '任务', '计划', '目标', '业务', '客户', '团队', '管理', '开发', '设计', '测试', '部署', '上线', '需求', '功能', '优化', '改进'],
      study: ['学习', '课程', '教程', '书籍', '考试', '复习', '练习', '作业', '笔记', '知识', '技能', '培训', '研究', '论文', '资料'],
      life: ['生活', '家庭', '朋友', '购物', '旅行', '娱乐', '电影', '音乐', '游戏', '休息', '放松', '聚会', '约会', '家务', '清洁'],
      health: ['健康', '运动', '锻炼', '跑步', '健身', '瑜伽', '饮食', '营养', '睡眠', '休息', '医院', '体检', '药物', '治疗'],
      technology: ['技术', '编程', '代码', '算法', '数据库', '服务器', '网络', '安全', '框架', '工具', '软件', '硬件', '系统', '平台', '应用']
    }

    Object.entries(keywords).forEach(([word, count]) => {
      let categorized = false

      for (const [category, patterns] of Object.entries(categoryPatterns)) {
        if (patterns.some(pattern => word.includes(pattern) || pattern.includes(word))) {
          categories[category].push({ word, count })
          categorized = true
          break
        }
      }

      if (!categorized) {
        categories.other.push({ word, count })
      }
    })

    return categories
  },

  /**
   * 生成关键词洞察
   *
   * @param {object} keywords 关键词对象
   * @param {object} categories 分类结果
   * @returns {array} 洞察数组
   */
  _generateKeywordInsights: function (keywords, categories) {
    const insights = []

    // 找出最活跃的类别
    const categoryStats = Object.entries(categories).map(([name, words]) => ({
      name,
      count: words.length,
      totalFreq: words.reduce((sum, item) => sum + item.count, 0)
    })).sort((a, b) => b.totalFreq - a.totalFreq)

    if (categoryStats.length > 0 && categoryStats[0].count > 0) {
      const topCategory = categoryStats[0]
      const categoryNames = {
        work: '工作',
        study: '学习',
        life: '生活',
        health: '健康',
        technology: '技术'
      }

      insights.push({
        type: 'info',
        message: `您的任务主要集中在${categoryNames[topCategory.name] || topCategory.name}领域`,
        priority: 'medium'
      })
    }

    // 高频关键词洞察
    const topKeywords = Object.entries(keywords).slice(0, 3)
    if (topKeywords.length > 0) {
      insights.push({
        type: 'info',
        message: `最常见的关键词：${topKeywords.map(([word]) => word).join('、')}`,
        priority: 'low'
      })
    }

    // 多样性分析
    const totalCategories = Object.values(categories).filter(cat => cat.length > 0).length
    if (totalCategories >= 4) {
      insights.push({
        type: 'positive',
        message: `任务类型多样化，涵盖${totalCategories}个不同领域`,
        priority: 'low'
      })
    } else if (totalCategories <= 2) {
      insights.push({
        type: 'suggestion',
        message: `任务类型相对集中，可考虑拓展其他领域的目标`,
        priority: 'medium'
      })
    }

    return insights
  },

  // ==================== 预测分析功能 ====================

  /**
   * 预测目标完成情况
   *
   * 功能说明：
   * - 基于历史数据预测目标完成时间
   * - 分析完成概率和风险因素
   * - 提供优化建议和行动计划
   * - 支持多种预测模型和算法
   *
   * @param {object} params 参数对象
   * @param {string} params.goal_id 目标ID（必需）
   * @param {boolean} [params.force_refresh] 是否强制刷新缓存数据
   * @returns {object} 预测数据
   */
  predictGoalCompletion: async function (params) {
    console.log('--- 调用 predictGoalCompletion ---', params)
    const { goal_id, force_refresh = false } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!goal_id) {
        return baseApi.handleError(new Error('目标ID不能为空'), 'GOAL_ID_REQUIRED', '目标ID不能为空')
      }

      const cacheKey = this._getCacheKey('goal_prediction', goal_id)

      // 检查缓存
      if (!force_refresh) {
        const cachedData = await this._getCache(cacheKey, 60) // 缓存1小时
        if (cachedData) {
          console.log('predictGoalCompletion 使用缓存数据')
          return baseApi.formatResponse(cachedData)
        }
      }

      // 获取目标详情
      const didaGoal = uniCloud.importObject('dida-goal')
      const goalResponse = await didaGoal.getGoal({ goal_id })

      if (!goalResponse.success) {
        return goalResponse
      }

      const goal = goalResponse.data

      // 获取目标进度历史
      const progressResponse = await this.getGoalProgress({ goal_id })
      if (!progressResponse.success) {
        return progressResponse
      }

      const progressData = progressResponse.data

      // 执行预测分析
      const prediction = this._performPredictionAnalysis(goal, progressData)

      // 风险评估
      const riskAssessment = this._assessCompletionRisk(goal, progressData, prediction)

      // 生成优化建议
      const recommendations = this._generateOptimizationRecommendations(goal, progressData, prediction, riskAssessment)

      const result = {
        goalInfo: {
          id: goal.id,
          title: goal.title,
          type: goal.type,
          currentProgress: goal.progress || 0,
          status: goal.status
        },
        prediction: {
          completionProbability: prediction.probability,
          estimatedCompletionDate: prediction.completionDate,
          daysRemaining: prediction.daysRemaining,
          confidence: prediction.confidence,
          model: prediction.model
        },
        riskAssessment: {
          riskLevel: riskAssessment.level, // 'low', 'medium', 'high'
          riskFactors: riskAssessment.factors,
          mitigationStrategies: riskAssessment.strategies
        },
        recommendations: {
          immediate: recommendations.immediate,
          longTerm: recommendations.longTerm,
          priority: recommendations.priority
        },
        insights: this._generatePredictionInsights(goal, prediction, riskAssessment)
      }

      // 缓存结果
      await this._setCache(cacheKey, result, 60)

      console.log('predictGoalCompletion 成功，目标ID：', goal_id)
      return baseApi.formatResponse(result)
    } catch (error) {
      console.error('predictGoalCompletion 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'PREDICT_GOAL_COMPLETION_FAILED', '预测目标完成失败')
    }
  },

  /**
   * 执行预测分析
   *
   * @param {object} goal 目标对象
   * @param {object} progressData 进度数据
   * @returns {object} 预测结果
   */
  _performPredictionAnalysis: function (goal, progressData) {
    const currentProgress = goal.progress || 0
    const trend = progressData.trend
    const progressHistory = progressData.progressHistory

    // 线性预测模型
    const linearPrediction = this._linearPredictionModel(currentProgress, trend, goal)

    // 趋势预测模型
    const trendPrediction = this._trendPredictionModel(progressHistory, goal)

    // 类型特定预测模型
    const typePrediction = this._typeSpecificPredictionModel(goal, progressHistory, trend)

    // 综合预测（加权平均）
    const predictions = [
      { model: 'linear', weight: 0.3, ...linearPrediction },
      { model: 'trend', weight: 0.4, ...trendPrediction },
      { model: 'type_specific', weight: 0.3, ...typePrediction }
    ]

    const weightedProbability = predictions.reduce((sum, pred) =>
      sum + pred.probability * pred.weight, 0
    )

    const weightedDays = predictions.reduce((sum, pred) =>
      sum + (pred.daysRemaining || 0) * pred.weight, 0
    )

    const avgConfidence = predictions.reduce((sum, pred) =>
      sum + pred.confidence, 0
    ) / predictions.length

    let completionDate = null
    if (weightedDays > 0) {
      const date = new Date()
      date.setDate(date.getDate() + Math.round(weightedDays))
      completionDate = this._formatDate(date)
    }

    return {
      probability: Math.round(weightedProbability),
      completionDate,
      daysRemaining: Math.round(weightedDays),
      confidence: Math.round(avgConfidence),
      model: 'ensemble',
      details: predictions
    }
  },

  /**
   * 线性预测模型
   *
   * @param {number} currentProgress 当前进度
   * @param {object} trend 趋势数据
   * @param {object} goal 目标对象
   * @returns {object} 预测结果
   */
  _linearPredictionModel: function (currentProgress, trend, goal) {
    if (currentProgress >= 100) {
      return { probability: 100, daysRemaining: 0, confidence: 100 }
    }

    if (trend.speed <= 0) {
      return { probability: 20, daysRemaining: null, confidence: 30 }
    }

    const remainingProgress = 100 - currentProgress
    const daysRemaining = remainingProgress / trend.speed

    // 基于截止日期调整概率
    let probability = 70
    if (goal.dueDate) {
      const dueDate = new Date(goal.dueDate)
      const today = new Date()
      const daysUntilDue = this._calculateDaysDiff(today, dueDate)

      if (daysRemaining <= daysUntilDue) {
        probability = Math.min(90, 70 + (daysUntilDue - daysRemaining) * 2)
      } else {
        probability = Math.max(20, 70 - (daysRemaining - daysUntilDue) * 3)
      }
    }

    return {
      probability: Math.round(probability),
      daysRemaining: Math.round(daysRemaining),
      confidence: trend.consistency
    }
  },

  /**
   * 趋势预测模型
   *
   * @param {array} progressHistory 进度历史
   * @param {object} goal 目标对象
   * @returns {object} 预测结果
   */
  _trendPredictionModel: function (progressHistory, goal) {
    if (progressHistory.length < 3) {
      return { probability: 50, daysRemaining: null, confidence: 40 }
    }

    // 计算加速度（进度变化的变化率）
    const recentProgress = progressHistory.slice(-7)
    const accelerations = []

    for (let i = 2; i < recentProgress.length; i++) {
      const prev = recentProgress[i-1].progress - recentProgress[i-2].progress
      const curr = recentProgress[i].progress - recentProgress[i-1].progress
      accelerations.push(curr - prev)
    }

    const avgAcceleration = accelerations.reduce((sum, acc) => sum + acc, 0) / accelerations.length

    // 基于加速度调整预测
    let probability = 60
    if (avgAcceleration > 0.5) {
      probability = 80 // 加速进行
    } else if (avgAcceleration < -0.5) {
      probability = 40 // 减速进行
    }

    // 计算趋势一致性
    const consistency = this._calculateTrendConsistency(progressHistory)

    return {
      probability: Math.round(probability),
      daysRemaining: null, // 趋势模型不直接预测时间
      confidence: Math.round(consistency)
    }
  },

  /**
   * 类型特定预测模型
   *
   * @param {object} goal 目标对象
   * @param {array} progressHistory 进度历史
   * @param {object} trend 趋势数据
   * @returns {object} 预测结果
   */
  _typeSpecificPredictionModel: function (goal, progressHistory, trend) {
    const currentProgress = goal.progress || 0

    switch (goal.type) {
      case 'phase':
        // 阶段性目标：基于截止日期的紧迫性
        return this._predictPhaseGoal(goal, currentProgress, trend)

      case 'habit':
        // 习惯目标：基于一致性和频率
        return this._predictHabitGoal(goal, progressHistory, trend)

      case 'permanent':
        // 永久目标：基于长期趋势
        return this._predictPermanentGoal(goal, currentProgress, trend)

      default:
        return { probability: 50, daysRemaining: null, confidence: 50 }
    }
  },

  /**
   * 预测阶段性目标
   *
   * @param {object} goal 目标对象
   * @param {number} currentProgress 当前进度
   * @param {object} trend 趋势数据
   * @returns {object} 预测结果
   */
  _predictPhaseGoal: function (goal, currentProgress, trend) {
    if (!goal.dueDate) {
      return { probability: 60, daysRemaining: null, confidence: 50 }
    }

    const dueDate = new Date(goal.dueDate)
    const today = new Date()
    const daysUntilDue = this._calculateDaysDiff(today, dueDate)

    if (daysUntilDue <= 0) {
      return { probability: currentProgress >= 100 ? 100 : 10, daysRemaining: 0, confidence: 90 }
    }

    const requiredSpeed = (100 - currentProgress) / daysUntilDue
    const currentSpeed = trend.speed || 0

    let probability = 50
    if (currentSpeed >= requiredSpeed) {
      probability = Math.min(90, 50 + (currentSpeed / requiredSpeed) * 30)
    } else {
      probability = Math.max(20, 50 - (requiredSpeed / Math.max(currentSpeed, 0.1)) * 10)
    }

    return {
      probability: Math.round(probability),
      daysRemaining: daysUntilDue,
      confidence: 80
    }
  },

  /**
   * 预测习惯目标
   *
   * @param {object} goal 目标对象
   * @param {array} progressHistory 进度历史
   * @param {object} trend 趋势数据
   * @returns {object} 预测结果
   */
  _predictHabitGoal: function (goal, progressHistory, trend) {
    // 习惯目标主要看一致性
    const consistency = trend.consistency || 0

    let probability = 40
    if (consistency >= 80) {
      probability = 85
    } else if (consistency >= 60) {
      probability = 70
    } else if (consistency >= 40) {
      probability = 55
    }

    // 习惯目标通常是长期的，不设具体完成时间
    return {
      probability: Math.round(probability),
      daysRemaining: null,
      confidence: Math.round(consistency)
    }
  },

  /**
   * 预测永久目标
   *
   * @param {object} goal 目标对象
   * @param {number} currentProgress 当前进度
   * @param {object} trend 趋势数据
   * @returns {object} 预测结果
   */
  _predictPermanentGoal: function (goal, currentProgress, trend) {
    // 永久目标基于长期趋势
    const speed = trend.speed || 0
    const consistency = trend.consistency || 0

    let probability = 60
    if (speed > 0 && consistency > 60) {
      probability = 75
    } else if (speed <= 0) {
      probability = 35
    }

    // 永久目标的时间预测不太准确
    let daysRemaining = null
    if (speed > 0) {
      daysRemaining = (100 - currentProgress) / speed
    }

    return {
      probability: Math.round(probability),
      daysRemaining: daysRemaining ? Math.round(daysRemaining) : null,
      confidence: Math.round((consistency + 40) / 2) // 降低置信度
    }
  },

  /**
   * 计算趋势一致性
   *
   * @param {array} progressHistory 进度历史
   * @returns {number} 一致性评分
   */
  _calculateTrendConsistency: function (progressHistory) {
    if (progressHistory.length < 3) return 50

    const changes = []
    for (let i = 1; i < progressHistory.length; i++) {
      changes.push(progressHistory[i].progress - progressHistory[i-1].progress)
    }

    const avgChange = changes.reduce((sum, change) => sum + change, 0) / changes.length
    const variance = changes.reduce((sum, change) => sum + Math.pow(change - avgChange, 2), 0) / changes.length

    // 一致性评分：方差越小，一致性越高
    const consistency = Math.max(0, 100 - variance * 20)
    return Math.round(consistency)
  },

  /**
   * 评估完成风险
   *
   * @param {object} goal 目标对象
   * @param {object} progressData 进度数据
   * @param {object} prediction 预测结果
   * @returns {object} 风险评估结果
   */
  _assessCompletionRisk: function (goal, progressData, prediction) {
    const riskFactors = []
    let riskScore = 0

    // 进度风险
    const currentProgress = goal.progress || 0
    if (currentProgress < 30) {
      riskFactors.push('进度较慢，当前完成度不足30%')
      riskScore += 20
    }

    // 趋势风险
    const trend = progressData.trend
    if (trend.direction === 'decreasing') {
      riskFactors.push('进度呈下降趋势')
      riskScore += 25
    } else if (trend.direction === 'stable' && trend.speed < 1) {
      riskFactors.push('进度停滞，缺乏明显推进')
      riskScore += 15
    }

    // 一致性风险
    if (trend.consistency < 50) {
      riskFactors.push('执行不够稳定，进度波动较大')
      riskScore += 15
    }

    // 时间风险（仅针对阶段性目标）
    if (goal.type === 'phase' && goal.dueDate) {
      const dueDate = new Date(goal.dueDate)
      const today = new Date()
      const daysUntilDue = this._calculateDaysDiff(today, dueDate)

      if (daysUntilDue <= 7) {
        riskFactors.push('截止日期临近，时间紧迫')
        riskScore += 30
      } else if (daysUntilDue <= 30) {
        riskFactors.push('距离截止日期不足一个月')
        riskScore += 15
      }
    }

    // 预测置信度风险
    if (prediction.confidence < 60) {
      riskFactors.push('预测置信度较低，存在不确定性')
      riskScore += 10
    }

    // 确定风险等级
    let riskLevel = 'low'
    if (riskScore >= 50) {
      riskLevel = 'high'
    } else if (riskScore >= 25) {
      riskLevel = 'medium'
    }

    // 生成缓解策略
    const strategies = this._generateMitigationStrategies(riskFactors, goal, trend)

    return {
      level: riskLevel,
      score: riskScore,
      factors: riskFactors,
      strategies
    }
  },

  /**
   * 生成缓解策略
   *
   * @param {array} riskFactors 风险因素
   * @param {object} goal 目标对象
   * @param {object} trend 趋势数据
   * @returns {array} 缓解策略数组
   */
  _generateMitigationStrategies: function (riskFactors, goal, trend) {
    const strategies = []

    riskFactors.forEach(factor => {
      if (factor.includes('进度较慢')) {
        strategies.push({
          type: 'action',
          title: '加快执行节奏',
          description: '增加每日投入时间，设置更频繁的检查点',
          priority: 'high'
        })
      }

      if (factor.includes('下降趋势')) {
        strategies.push({
          type: 'analysis',
          title: '分析阻碍因素',
          description: '识别导致进度下降的具体原因，制定针对性解决方案',
          priority: 'high'
        })
      }

      if (factor.includes('进度停滞')) {
        strategies.push({
          type: 'motivation',
          title: '重新激发动力',
          description: '回顾目标意义，调整执行方法，寻找新的推进动力',
          priority: 'medium'
        })
      }

      if (factor.includes('执行不够稳定')) {
        strategies.push({
          type: 'planning',
          title: '制定稳定计划',
          description: '建立固定的执行时间和节奏，减少外界干扰',
          priority: 'medium'
        })
      }

      if (factor.includes('时间紧迫')) {
        strategies.push({
          type: 'urgency',
          title: '紧急行动计划',
          description: '集中资源，优先处理关键任务，必要时调整目标范围',
          priority: 'critical'
        })
      }
    })

    // 添加通用策略
    if (goal.type === 'habit') {
      strategies.push({
        type: 'habit',
        title: '建立习惯提醒',
        description: '设置每日提醒，建立固定的执行时间和环境',
        priority: 'medium'
      })
    }

    return strategies
  },

  /**
   * 生成优化建议
   *
   * @param {object} goal 目标对象
   * @param {object} progressData 进度数据
   * @param {object} prediction 预测结果
   * @param {object} riskAssessment 风险评估
   * @returns {object} 优化建议
   */
  _generateOptimizationRecommendations: function (goal, progressData, prediction, riskAssessment) {
    const immediate = []
    const longTerm = []
    let priority = 'medium'

    // 基于风险等级确定优先级
    if (riskAssessment.level === 'high') {
      priority = 'high'
    } else if (riskAssessment.level === 'low' && prediction.probability >= 80) {
      priority = 'low'
    }

    // 立即行动建议
    if (prediction.probability < 60) {
      immediate.push({
        action: '重新评估目标',
        description: '当前完成概率较低，建议重新评估目标的可行性和时间安排',
        impact: 'high'
      })
    }

    if (progressData.trend.speed < 1 && goal.type === 'phase') {
      immediate.push({
        action: '加快执行速度',
        description: '当前进度速度不足，需要增加投入或优化执行方法',
        impact: 'high'
      })
    }

    if (progressData.trend.consistency < 60) {
      immediate.push({
        action: '建立稳定节奏',
        description: '执行不够稳定，建议制定固定的工作计划和检查机制',
        impact: 'medium'
      })
    }

    // 长期优化建议
    longTerm.push({
      strategy: '数据驱动优化',
      description: '定期分析进度数据，根据趋势调整策略和方法',
      timeline: '持续进行'
    })

    if (goal.type === 'habit') {
      longTerm.push({
        strategy: '习惯强化',
        description: '通过奖励机制和环境设计，强化习惯的自动化程度',
        timeline: '3-6个月'
      })
    }

    longTerm.push({
      strategy: '目标分解',
      description: '将大目标分解为更小的里程碑，提高成就感和执行动力',
      timeline: '1-2周内完成'
    })

    return {
      immediate,
      longTerm,
      priority
    }
  },

  /**
   * 生成预测洞察
   *
   * @param {object} goal 目标对象
   * @param {object} prediction 预测结果
   * @param {object} riskAssessment 风险评估
   * @returns {array} 洞察数组
   */
  _generatePredictionInsights: function (goal, prediction, riskAssessment) {
    const insights = []

    // 完成概率洞察
    if (prediction.probability >= 80) {
      insights.push({
        type: 'positive',
        message: `目标完成概率很高（${prediction.probability}%），保持当前节奏即可`,
        priority: 'low'
      })
    } else if (prediction.probability >= 60) {
      insights.push({
        type: 'info',
        message: `目标有较好的完成前景（${prediction.probability}%），建议适当优化执行策略`,
        priority: 'medium'
      })
    } else {
      insights.push({
        type: 'warning',
        message: `目标完成存在挑战（${prediction.probability}%），需要重点关注和调整`,
        priority: 'high'
      })
    }

    // 风险洞察
    if (riskAssessment.level === 'high') {
      insights.push({
        type: 'warning',
        message: `检测到高风险因素，建议立即采取缓解措施`,
        priority: 'high'
      })
    } else if (riskAssessment.level === 'medium') {
      insights.push({
        type: 'info',
        message: `存在中等风险，建议关注并适时调整策略`,
        priority: 'medium'
      })
    }

    // 时间洞察
    if (prediction.completionDate && goal.dueDate) {
      const predictedDate = new Date(prediction.completionDate)
      const dueDate = new Date(goal.dueDate)

      if (predictedDate <= dueDate) {
        insights.push({
          type: 'positive',
          message: `预计可在截止日期前完成（${prediction.completionDate}）`,
          priority: 'low'
        })
      } else {
        const delayDays = this._calculateDaysDiff(dueDate, predictedDate)
        insights.push({
          type: 'warning',
          message: `预计将延期${delayDays}天完成，建议调整计划`,
          priority: 'high'
        })
      }
    }

    // 置信度洞察
    if (prediction.confidence < 60) {
      insights.push({
        type: 'info',
        message: `预测置信度较低（${prediction.confidence}%），建议增加数据收集频率`,
        priority: 'medium'
      })
    }

    return insights
  },

  // ==================== 报告生成功能 ====================

  /**
   * 生成目标报告
   *
   * 功能说明：
   * - 生成指定目标的详细分析报告
   * - 包含进度分析、预测结果、风险评估
   * - 提供可视化数据和执行建议
   * - 支持多种报告格式和样式
   *
   * @param {object} params 参数对象
   * @param {string} params.goal_id 目标ID（必需）
   * @param {boolean} [params.force_refresh] 是否强制刷新缓存数据
   * @returns {object} 目标报告数据
   */
  generateGoalReport: async function (params) {
    console.log('--- 调用 generateGoalReport ---', params)
    const { goal_id, force_refresh = false } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!goal_id) {
        return baseApi.handleError(new Error('目标ID不能为空'), 'GOAL_ID_REQUIRED', '目标ID不能为空')
      }

      const cacheKey = this._getCacheKey('goal_report', goal_id)

      // 检查缓存
      if (!force_refresh) {
        const cachedData = await this._getCache(cacheKey, 120) // 缓存2小时
        if (cachedData) {
          console.log('generateGoalReport 使用缓存数据')
          return baseApi.formatResponse(cachedData)
        }
      }

      // 获取目标详情
      const didaGoal = uniCloud.importObject('dida-goal')
      const goalResponse = await didaGoal.getGoal({ goal_id })

      if (!goalResponse.success) {
        return goalResponse
      }

      const goal = goalResponse.data

      // 获取相关分析数据
      const [progressResponse, predictionResponse] = await Promise.all([
        this.getGoalProgress({ goal_id }),
        this.predictGoalCompletion({ goal_id })
      ])

      if (!progressResponse.success || !predictionResponse.success) {
        throw new Error('获取分析数据失败')
      }

      const progressData = progressResponse.data
      const predictionData = predictionResponse.data

      // 生成报告内容
      const report = {
        metadata: {
          goalId: goal.id,
          goalTitle: goal.title,
          reportDate: this._formatDate(new Date()),
          reportType: 'goal_analysis',
          version: '1.0'
        },
        executive_summary: this._generateExecutiveSummary(goal, progressData, predictionData),
        progress_analysis: {
          current_status: {
            progress: goal.progress || 0,
            status: goal.status,
            trend: progressData.trend.direction,
            consistency: progressData.trend.consistency
          },
          historical_data: progressData.progressHistory,
          milestones: this._identifyMilestones(progressData.progressHistory),
          performance_metrics: this._calculatePerformanceMetrics(progressData)
        },
        prediction_analysis: {
          completion_forecast: {
            probability: predictionData.prediction.completionProbability,
            estimated_date: predictionData.prediction.estimatedCompletionDate,
            confidence: predictionData.prediction.confidence
          },
          risk_assessment: predictionData.riskAssessment,
          scenario_analysis: this._generateScenarioAnalysis(goal, progressData)
        },
        recommendations: {
          immediate_actions: predictionData.recommendations.immediate,
          strategic_improvements: predictionData.recommendations.longTerm,
          priority_level: predictionData.recommendations.priority
        },
        insights_and_conclusions: [
          ...progressData.insights,
          ...predictionData.insights
        ],
        appendix: {
          data_sources: ['目标进度历史', '预测分析模型', '风险评估算法'],
          methodology: '基于历史数据的趋势分析和机器学习预测',
          limitations: this._getReportLimitations(goal, progressData)
        }
      }

      // 缓存结果
      await this._setCache(cacheKey, report, 120)

      console.log('generateGoalReport 成功，目标ID：', goal_id)
      return baseApi.formatResponse(report)
    } catch (error) {
      console.error('generateGoalReport 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GENERATE_GOAL_REPORT_FAILED', '生成目标报告失败')
    }
  },

  /**
   * 生成执行摘要
   *
   * @param {object} goal 目标对象
   * @param {object} progressData 进度数据
   * @param {object} predictionData 预测数据
   * @returns {object} 执行摘要
   */
  _generateExecutiveSummary: function (goal, progressData, predictionData) {
    const currentProgress = goal.progress || 0
    const trend = progressData.trend.direction
    const probability = predictionData.prediction.completionProbability
    const riskLevel = predictionData.riskAssessment.riskLevel

    let status = 'on_track'
    if (probability < 60 || riskLevel === 'high') {
      status = 'at_risk'
    } else if (probability >= 80 && riskLevel === 'low') {
      status = 'ahead'
    }

    const summary = {
      overall_status: status,
      key_metrics: {
        current_progress: currentProgress,
        completion_probability: probability,
        trend_direction: trend,
        risk_level: riskLevel
      },
      key_findings: [],
      critical_actions: []
    }

    // 关键发现
    if (currentProgress >= 80) {
      summary.key_findings.push('目标接近完成，进展良好')
    } else if (currentProgress < 30) {
      summary.key_findings.push('目标进度较慢，需要加强推进')
    }

    if (trend === 'increasing') {
      summary.key_findings.push('进度趋势积极向上')
    } else if (trend === 'decreasing') {
      summary.key_findings.push('进度出现下降趋势，需要关注')
    }

    // 关键行动
    if (riskLevel === 'high') {
      summary.critical_actions.push('立即识别和解决高风险因素')
    }

    if (probability < 60) {
      summary.critical_actions.push('重新评估目标可行性和执行策略')
    }

    if (progressData.trend.consistency < 50) {
      summary.critical_actions.push('建立更稳定的执行节奏')
    }

    return summary
  },

  /**
   * 识别里程碑
   *
   * @param {array} progressHistory 进度历史
   * @returns {array} 里程碑数组
   */
  _identifyMilestones: function (progressHistory) {
    const milestones = []
    const thresholds = [25, 50, 75, 100]

    thresholds.forEach(threshold => {
      const milestone = progressHistory.find(entry => entry.progress >= threshold)
      if (milestone) {
        milestones.push({
          threshold,
          date: milestone.date,
          progress: milestone.progress,
          achieved: true
        })
      } else {
        milestones.push({
          threshold,
          date: null,
          progress: null,
          achieved: false
        })
      }
    })

    return milestones
  },

  /**
   * 计算性能指标
   *
   * @param {object} progressData 进度数据
   * @returns {object} 性能指标
   */
  _calculatePerformanceMetrics: function (progressData) {
    const history = progressData.progressHistory
    const trend = progressData.trend

    if (history.length < 2) {
      return {
        average_daily_progress: 0,
        peak_performance_day: null,
        consistency_score: 0,
        acceleration: 0
      }
    }

    // 平均每日进度
    const totalProgress = history[history.length - 1].progress - history[0].progress
    const days = history.length - 1
    const averageDailyProgress = days > 0 ? (totalProgress / days).toFixed(2) : 0

    // 最佳表现日
    let peakDay = null
    let maxDailyProgress = 0

    for (let i = 1; i < history.length; i++) {
      const dailyProgress = history[i].progress - history[i-1].progress
      if (dailyProgress > maxDailyProgress) {
        maxDailyProgress = dailyProgress
        peakDay = {
          date: history[i].date,
          progress: dailyProgress
        }
      }
    }

    // 加速度（最近一周vs前一周的进度变化）
    let acceleration = 0
    if (history.length >= 14) {
      const recentWeek = history.slice(-7)
      const previousWeek = history.slice(-14, -7)

      const recentProgress = recentWeek[recentWeek.length - 1].progress - recentWeek[0].progress
      const previousProgress = previousWeek[previousWeek.length - 1].progress - previousWeek[0].progress

      acceleration = recentProgress - previousProgress
    }

    return {
      average_daily_progress: parseFloat(averageDailyProgress),
      peak_performance_day: peakDay,
      consistency_score: trend.consistency,
      acceleration: parseFloat(acceleration.toFixed(2))
    }
  },

  /**
   * 生成情景分析
   *
   * @param {object} goal 目标对象
   * @param {object} progressData 进度数据
   * @returns {object} 情景分析
   */
  _generateScenarioAnalysis: function (goal, progressData) {
    const currentProgress = goal.progress || 0
    const currentSpeed = progressData.trend.speed || 0

    // 乐观情景：速度提升50%
    const optimisticSpeed = currentSpeed * 1.5
    const optimisticDays = optimisticSpeed > 0 ? (100 - currentProgress) / optimisticSpeed : null

    // 悲观情景：速度下降30%
    const pessimisticSpeed = currentSpeed * 0.7
    const pessimisticDays = pessimisticSpeed > 0 ? (100 - currentProgress) / pessimisticSpeed : null

    // 现实情景：保持当前速度
    const realisticDays = currentSpeed > 0 ? (100 - currentProgress) / currentSpeed : null

    const scenarios = {
      optimistic: {
        description: '执行效率提升，进度加快',
        completion_days: optimisticDays ? Math.round(optimisticDays) : null,
        probability: 30,
        conditions: ['增加投入时间', '优化执行方法', '减少外界干扰']
      },
      realistic: {
        description: '保持当前执行节奏',
        completion_days: realisticDays ? Math.round(realisticDays) : null,
        probability: 50,
        conditions: ['维持现状', '无重大变化']
      },
      pessimistic: {
        description: '遇到阻碍，进度放缓',
        completion_days: pessimisticDays ? Math.round(pessimisticDays) : null,
        probability: 20,
        conditions: ['出现意外困难', '资源投入减少', '优先级下降']
      }
    }

    return scenarios
  },

  /**
   * 获取报告限制说明
   *
   * @param {object} goal 目标对象
   * @param {object} progressData 进度数据
   * @returns {array} 限制说明数组
   */
  _getReportLimitations: function (goal, progressData) {
    const limitations = []

    if (progressData.progressHistory.length < 7) {
      limitations.push('历史数据较少，预测准确性可能受限')
    }

    if (progressData.trend.consistency < 60) {
      limitations.push('进度波动较大，趋势分析可能不够稳定')
    }

    if (goal.type === 'permanent') {
      limitations.push('永久目标的完成时间预测具有较大不确定性')
    }

    limitations.push('预测基于历史趋势，未考虑未来可能的重大变化')
    limitations.push('建议结合实际情况和专业判断使用本报告')

    return limitations
  },

  /**
   * 生成每周总结
   *
   * 功能说明：
   * - 生成过去一周的综合数据分析报告
   * - 包含任务完成情况、目标进展、效率分析
   * - 提供下周计划建议和优化方向
   * - 支持个性化洞察和趋势分析
   *
   * @param {object} params 参数对象
   * @param {boolean} [params.force_refresh] 是否强制刷新缓存数据
   * @returns {object} 每周总结数据
   */
  generateWeeklySummary: async function (params) {
    console.log('--- 调用 generateWeeklySummary ---', params)
    const { force_refresh = false } = params || {}

    try {
      const baseApi = this._getBaseApi()
      const cacheKey = this._getCacheKey('weekly_summary', this._getWeekKey())

      // 检查缓存
      if (!force_refresh) {
        const cachedData = await this._getCache(cacheKey, 180) // 缓存3小时
        if (cachedData) {
          console.log('generateWeeklySummary 使用缓存数据')
          return baseApi.formatResponse(cachedData)
        }
      }

      // 获取基础数据
      const [taskStats, goalStats, allGoals, allTasks] = await Promise.all([
        this.getTaskStatistics({ days: 7 }),
        this.getGoalStatistics(),
        this._getAllGoals(),
        this._getAllTasks()
      ])

      if (!taskStats.success || !goalStats.success) {
        throw new Error('获取统计数据失败')
      }

      // 生成周总结
      const summary = {
        metadata: {
          week_period: this._getWeekPeriod(),
          generation_date: this._formatDate(new Date()),
          report_type: 'weekly_summary',
          version: '1.0'
        },
        executive_overview: this._generateWeeklyOverview(taskStats.data, goalStats.data),
        task_performance: {
          completion_metrics: this._analyzeWeeklyTaskCompletion(taskStats.data),
          productivity_insights: this._generateProductivityInsights(taskStats.data),
          priority_analysis: this._analyzePriorityDistribution(taskStats.data)
        },
        goal_progress: {
          overall_progress: this._analyzeWeeklyGoalProgress(allGoals),
          milestone_achievements: this._identifyWeeklyMilestones(allGoals),
          goal_insights: this._generateWeeklyGoalInsights(allGoals)
        },
        efficiency_analysis: {
          time_management: this._analyzeTimeManagement(taskStats.data),
          focus_areas: this._identifyFocusAreas(taskStats.data, allGoals),
          improvement_opportunities: this._identifyImprovementOpportunities(taskStats.data, goalStats.data)
        },
        next_week_planning: {
          recommended_actions: this._generateNextWeekActions(taskStats.data, goalStats.data, allGoals),
          priority_suggestions: this._suggestNextWeekPriorities(allGoals, allTasks),
          optimization_tips: this._generateOptimizationTips(taskStats.data, goalStats.data)
        },
        insights_and_trends: this._generateWeeklyInsights(taskStats.data, goalStats.data, allGoals),
        appendix: {
          data_summary: {
            tasks_analyzed: taskStats.data.overview.totalTasks,
            goals_tracked: goalStats.data.overview.totalGoals,
            analysis_period: '过去7天'
          },
          methodology: '基于任务完成数据和目标进度的综合分析',
          next_report: this._getNextReportDate()
        }
      }

      // 缓存结果
      await this._setCache(cacheKey, summary, 180)

      console.log('generateWeeklySummary 成功')
      return baseApi.formatResponse(summary)
    } catch (error) {
      console.error('generateWeeklySummary 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GENERATE_WEEKLY_SUMMARY_FAILED', '生成每周总结失败')
    }
  },

  /**
   * 获取周标识
   *
   * @returns {string} 周标识
   */
  _getWeekKey: function () {
    const now = new Date()
    const year = now.getFullYear()
    const week = this._getWeekNumber(now)
    return `${year}_W${week}`
  },

  /**
   * 获取周数
   *
   * @param {Date} date 日期
   * @returns {number} 周数
   */
  _getWeekNumber: function (date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()))
    const dayNum = d.getUTCDay() || 7
    d.setUTCDate(d.getUTCDate() + 4 - dayNum)
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1))
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7)
  },

  /**
   * 获取周期间
   *
   * @returns {object} 周期间信息
   */
  _getWeekPeriod: function () {
    const now = new Date()
    const startOfWeek = new Date(now)
    startOfWeek.setDate(now.getDate() - now.getDay() + 1) // 周一

    const endOfWeek = new Date(startOfWeek)
    endOfWeek.setDate(startOfWeek.getDate() + 6) // 周日

    return {
      start_date: this._formatDate(startOfWeek),
      end_date: this._formatDate(endOfWeek),
      week_number: this._getWeekNumber(now),
      year: now.getFullYear()
    }
  },

  /**
   * 生成周概览
   *
   * @param {object} taskStats 任务统计
   * @param {object} goalStats 目标统计
   * @returns {object} 周概览
   */
  _generateWeeklyOverview: function (taskStats, goalStats) {
    const taskCompletion = taskStats.overview.completionRate
    const goalProgress = goalStats.overview.averageProgress

    let overallRating = 'average'
    if (taskCompletion >= 80 && goalProgress >= 70) {
      overallRating = 'excellent'
    } else if (taskCompletion >= 60 && goalProgress >= 50) {
      overallRating = 'good'
    } else if (taskCompletion < 40 || goalProgress < 30) {
      overallRating = 'needs_improvement'
    }

    return {
      overall_rating: overallRating,
      key_achievements: this._identifyKeyAchievements(taskStats, goalStats),
      main_challenges: this._identifyMainChallenges(taskStats, goalStats),
      week_highlights: this._generateWeekHighlights(taskStats, goalStats)
    }
  },

  /**
   * 识别关键成就
   *
   * @param {object} taskStats 任务统计
   * @param {object} goalStats 目标统计
   * @returns {array} 关键成就数组
   */
  _identifyKeyAchievements: function (taskStats, goalStats) {
    const achievements = []

    if (taskStats.overview.completionRate >= 80) {
      achievements.push({
        type: 'task_completion',
        description: `任务完成率达到${taskStats.overview.completionRate}%，表现优秀`,
        impact: 'high'
      })
    }

    if (goalStats.overview.completedGoals > 0) {
      achievements.push({
        type: 'goal_completion',
        description: `完成了${goalStats.overview.completedGoals}个目标`,
        impact: 'high'
      })
    }

    if (taskStats.trends.efficiency.level === 'high') {
      achievements.push({
        type: 'efficiency',
        description: `工作效率评分达到${taskStats.trends.efficiency.score}分，效率很高`,
        impact: 'medium'
      })
    }

    if (parseFloat(taskStats.trends.weeklyGrowth) > 10) {
      achievements.push({
        type: 'growth',
        description: `任务处理量较上周增长${taskStats.trends.weeklyGrowth}%`,
        impact: 'medium'
      })
    }

    return achievements
  },

  /**
   * 识别主要挑战
   *
   * @param {object} taskStats 任务统计
   * @param {object} goalStats 目标统计
   * @returns {array} 主要挑战数组
   */
  _identifyMainChallenges: function (taskStats, goalStats) {
    const challenges = []

    if (taskStats.overview.completionRate < 50) {
      challenges.push({
        type: 'low_completion',
        description: `任务完成率仅为${taskStats.overview.completionRate}%，需要改进`,
        severity: 'high'
      })
    }

    if (goalStats.overview.averageProgress < 40) {
      challenges.push({
        type: 'slow_progress',
        description: `目标平均进度为${goalStats.overview.averageProgress}%，推进较慢`,
        severity: 'medium'
      })
    }

    if (taskStats.trends.efficiency.level === 'low') {
      challenges.push({
        type: 'low_efficiency',
        description: `工作效率评分为${taskStats.trends.efficiency.score}分，效率偏低`,
        severity: 'medium'
      })
    }

    if (parseFloat(taskStats.trends.weeklyGrowth) < -10) {
      challenges.push({
        type: 'declining_activity',
        description: `任务处理量较上周下降${Math.abs(taskStats.trends.weeklyGrowth)}%`,
        severity: 'medium'
      })
    }

    return challenges
  },

  /**
   * 生成周亮点
   *
   * @param {object} taskStats 任务统计
   * @param {object} goalStats 目标统计
   * @returns {array} 周亮点数组
   */
  _generateWeekHighlights: function (taskStats, goalStats) {
    const highlights = []

    // 任务亮点
    if (taskStats.timeAnalysis.peakDay) {
      highlights.push(`${taskStats.timeAnalysis.peakDay.date} 是最高效的一天，完成了${taskStats.timeAnalysis.peakDay.taskCount}个任务`)
    }

    // 目标亮点
    if (goalStats.timeAnalysis.createdThisMonth > 0) {
      highlights.push(`本月新增了${goalStats.timeAnalysis.createdThisMonth}个目标`)
    }

    if (goalStats.timeAnalysis.completedThisMonth > 0) {
      highlights.push(`本月完成了${goalStats.timeAnalysis.completedThisMonth}个目标`)
    }

    // 效率亮点
    if (taskStats.timeAnalysis.averageCompletionTime) {
      highlights.push(`平均任务完成时间为${taskStats.timeAnalysis.averageCompletionTime}小时`)
    }

    return highlights
  },

  /**
   * 分析每周任务完成情况
   *
   * @param {object} taskStats 任务统计
   * @returns {object} 任务完成分析
   */
  _analyzeWeeklyTaskCompletion: function (taskStats) {
    const dailyStats = taskStats.timeAnalysis.dailyStats || []

    const completionTrend = dailyStats.map(day => ({
      date: day.date,
      completed: day.completed,
      total: day.total,
      rate: day.total > 0 ? (day.completed / day.total * 100).toFixed(1) : 0
    }))

    const bestDay = dailyStats.reduce((best, day) =>
      day.completed > (best?.completed || 0) ? day : best, null
    )

    const worstDay = dailyStats.reduce((worst, day) =>
      day.total > 0 && (day.completed / day.total) < ((worst?.completed || 1) / (worst?.total || 1)) ? day : worst, null
    )

    return {
      daily_trend: completionTrend,
      best_performance: bestDay ? {
        date: bestDay.date,
        completed: bestDay.completed,
        total: bestDay.total
      } : null,
      worst_performance: worstDay ? {
        date: worstDay.date,
        completed: worstDay.completed,
        total: worstDay.total
      } : null,
      consistency_score: this._calculateWeeklyConsistency(dailyStats)
    }
  },

  /**
   * 计算周一致性
   *
   * @param {array} dailyStats 每日统计
   * @returns {number} 一致性评分
   */
  _calculateWeeklyConsistency: function (dailyStats) {
    if (dailyStats.length === 0) return 0

    const completionRates = dailyStats.map(day =>
      day.total > 0 ? day.completed / day.total : 0
    )

    const avgRate = completionRates.reduce((sum, rate) => sum + rate, 0) / completionRates.length
    const variance = completionRates.reduce((sum, rate) => sum + Math.pow(rate - avgRate, 2), 0) / completionRates.length

    // 一致性评分：方差越小，一致性越高
    const consistency = Math.max(0, 100 - variance * 200)
    return Math.round(consistency)
  },

  /**
   * 生成生产力洞察
   *
   * @param {object} taskStats 任务统计
   * @returns {array} 生产力洞察数组
   */
  _generateProductivityInsights: function (taskStats) {
    const insights = []

    const efficiency = taskStats.trends.efficiency
    if (efficiency.level === 'high') {
      insights.push({
        type: 'positive',
        message: `工作效率很高（${efficiency.score}分），继续保持当前工作节奏`,
        actionable: false
      })
    } else if (efficiency.level === 'low') {
      insights.push({
        type: 'improvement',
        message: `工作效率有待提升（${efficiency.score}分），建议优化时间管理`,
        actionable: true
      })
    }

    const avgPerDay = parseFloat(taskStats.overview.averagePerDay)
    if (avgPerDay > 5) {
      insights.push({
        type: 'warning',
        message: `每日平均任务量较高（${avgPerDay}个），注意工作负载平衡`,
        actionable: true
      })
    } else if (avgPerDay < 2) {
      insights.push({
        type: 'suggestion',
        message: `每日任务量较少（${avgPerDay}个），可考虑增加工作强度`,
        actionable: true
      })
    }

    return insights
  },

  /**
   * 获取下次报告日期
   *
   * @returns {string} 下次报告日期
   */
  _getNextReportDate: function () {
    const nextWeek = new Date()
    nextWeek.setDate(nextWeek.getDate() + 7)
    return this._formatDate(nextWeek)
  },

  /**
   * 分析每周目标进展
   *
   * @param {array} allGoals 所有目标
   * @returns {object} 目标进展分析
   */
  _analyzeWeeklyGoalProgress: function (allGoals) {
    const activeGoals = allGoals.filter(goal => goal.status === 'active')
    const totalProgress = activeGoals.reduce((sum, goal) => sum + (goal.progress || 0), 0)
    const averageProgress = activeGoals.length > 0 ? (totalProgress / activeGoals.length).toFixed(1) : 0

    // 按进度分组
    const progressGroups = {
      high: activeGoals.filter(goal => (goal.progress || 0) >= 75).length,
      medium: activeGoals.filter(goal => (goal.progress || 0) >= 25 && (goal.progress || 0) < 75).length,
      low: activeGoals.filter(goal => (goal.progress || 0) < 25).length
    }

    return {
      total_active_goals: activeGoals.length,
      average_progress: parseFloat(averageProgress),
      progress_distribution: progressGroups,
      goals_near_completion: progressGroups.high,
      goals_needing_attention: progressGroups.low
    }
  },

  /**
   * 识别每周里程碑
   *
   * @param {array} allGoals 所有目标
   * @returns {array} 里程碑数组
   */
  _identifyWeeklyMilestones: function (allGoals) {
    const milestones = []

    // 本周完成的目标
    const completedThisWeek = allGoals.filter(goal => {
      if (goal.status !== 'completed' || !goal.modifiedTime) return false
      const completedDate = new Date(goal.modifiedTime)
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      return completedDate >= weekAgo
    })

    completedThisWeek.forEach(goal => {
      milestones.push({
        type: 'completion',
        goal_title: goal.title,
        achievement_date: this._formatDate(goal.modifiedTime),
        significance: 'high'
      })
    })

    // 本周达到重要进度节点的目标
    allGoals.forEach(goal => {
      const progress = goal.progress || 0
      if (progress >= 25 && progress < 50) {
        milestones.push({
          type: 'progress_milestone',
          goal_title: goal.title,
          milestone: '25%',
          significance: 'medium'
        })
      } else if (progress >= 50 && progress < 75) {
        milestones.push({
          type: 'progress_milestone',
          goal_title: goal.title,
          milestone: '50%',
          significance: 'medium'
        })
      } else if (progress >= 75 && progress < 100) {
        milestones.push({
          type: 'progress_milestone',
          goal_title: goal.title,
          milestone: '75%',
          significance: 'high'
        })
      }
    })

    return milestones
  },

  /**
   * 生成每周目标洞察
   *
   * @param {array} allGoals 所有目标
   * @returns {array} 目标洞察数组
   */
  _generateWeeklyGoalInsights: function (allGoals) {
    const insights = []
    const activeGoals = allGoals.filter(goal => goal.status === 'active')

    // 目标类型分析
    const typeDistribution = {}
    activeGoals.forEach(goal => {
      typeDistribution[goal.type] = (typeDistribution[goal.type] || 0) + 1
    })

    const dominantType = Object.entries(typeDistribution).reduce((max, [type, count]) =>
      count > (max[1] || 0) ? [type, count] : max, ['', 0]
    )

    if (dominantType[1] > 0) {
      const typeNames = { phase: '阶段性', habit: '习惯', permanent: '永久' }
      insights.push({
        type: 'info',
        message: `当前主要关注${typeNames[dominantType[0]] || dominantType[0]}目标（${dominantType[1]}个）`,
        category: 'goal_focus'
      })
    }

    // 进度分析
    const stagnantGoals = activeGoals.filter(goal => {
      const progress = goal.progress || 0
      return progress > 0 && progress < 10 // 进度很少的目标
    })

    if (stagnantGoals.length > 0) {
      insights.push({
        type: 'warning',
        message: `有${stagnantGoals.length}个目标进度缓慢，需要重点关注`,
        category: 'progress_concern'
      })
    }

    // 即将到期的目标
    const urgentGoals = activeGoals.filter(goal => {
      if (!goal.dueDate) return false
      const dueDate = new Date(goal.dueDate)
      const today = new Date()
      const daysUntilDue = this._calculateDaysDiff(today, dueDate)
      return daysUntilDue <= 14 && daysUntilDue > 0
    })

    if (urgentGoals.length > 0) {
      insights.push({
        type: 'urgent',
        message: `有${urgentGoals.length}个目标将在两周内到期，需要加快推进`,
        category: 'deadline_alert'
      })
    }

    return insights
  },

  /**
   * 分析时间管理
   *
   * @param {object} taskStats 任务统计
   * @returns {object} 时间管理分析
   */
  _analyzeTimeManagement: function (taskStats) {
    const dailyStats = taskStats.timeAnalysis.dailyStats || []

    // 工作负载分析
    const workloadVariation = this._calculateWorkloadVariation(dailyStats)
    const peakWorkdays = dailyStats.filter(day => day.total > 5)
    const lightWorkdays = dailyStats.filter(day => day.total < 2)

    return {
      workload_consistency: workloadVariation < 2 ? 'stable' : 'variable',
      peak_workdays: peakWorkdays.length,
      light_workdays: lightWorkdays.length,
      average_completion_time: taskStats.timeAnalysis.averageCompletionTime || 0,
      time_management_score: this._calculateTimeManagementScore(taskStats)
    }
  },

  /**
   * 计算工作负载变化
   *
   * @param {array} dailyStats 每日统计
   * @returns {number} 工作负载变化系数
   */
  _calculateWorkloadVariation: function (dailyStats) {
    if (dailyStats.length === 0) return 0

    const taskCounts = dailyStats.map(day => day.total)
    const avgTasks = taskCounts.reduce((sum, count) => sum + count, 0) / taskCounts.length
    const variance = taskCounts.reduce((sum, count) => sum + Math.pow(count - avgTasks, 2), 0) / taskCounts.length

    return Math.sqrt(variance)
  },

  /**
   * 计算时间管理评分
   *
   * @param {object} taskStats 任务统计
   * @returns {number} 时间管理评分
   */
  _calculateTimeManagementScore: function (taskStats) {
    let score = 50 // 基础分

    // 完成率影响
    const completionRate = taskStats.overview.completionRate
    score += (completionRate - 50) * 0.5

    // 效率影响
    const efficiency = taskStats.trends.efficiency.score
    score += (efficiency - 50) * 0.3

    // 一致性影响
    const dailyStats = taskStats.timeAnalysis.dailyStats || []
    const consistency = this._calculateWeeklyConsistency(dailyStats)
    score += (consistency - 50) * 0.2

    return Math.max(0, Math.min(100, Math.round(score)))
  },

  /**
   * 识别关注领域
   *
   * @param {object} taskStats 任务统计
   * @param {array} allGoals 所有目标
   * @returns {array} 关注领域数组
   */
  _identifyFocusAreas: function (taskStats, allGoals) {
    const focusAreas = []

    // 基于任务项目分布
    const projectStats = (taskStats && taskStats.distribution && taskStats.distribution.byProject) || {}
    const topProjects = Object.entries(projectStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)

    topProjects.forEach(([project, count]) => {
      focusAreas.push({
        area: project,
        type: 'project',
        activity_level: count,
        source: 'task_analysis'
      })
    })

    // 基于目标关键词
    const goalKeywords = {}
    allGoals.forEach(goal => {
      if (goal.keywords) {
        goal.keywords.forEach(keyword => {
          goalKeywords[keyword] = (goalKeywords[keyword] || 0) + 1
        })
      }
    })

    const topKeywords = Object.entries(goalKeywords)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 2)

    topKeywords.forEach(([keyword, count]) => {
      focusAreas.push({
        area: keyword,
        type: 'keyword',
        activity_level: count,
        source: 'goal_analysis'
      })
    })

    return focusAreas
  },

  /**
   * 识别改进机会
   *
   * @param {object} taskStats 任务统计
   * @param {object} goalStats 目标统计
   * @returns {array} 改进机会数组
   */
  _identifyImprovementOpportunities: function (taskStats, goalStats) {
    const opportunities = []

    // 任务完成率改进
    if (taskStats.overview.completionRate < 70) {
      opportunities.push({
        area: 'task_completion',
        current_performance: `${taskStats.overview.completionRate}%`,
        improvement_potential: 'high',
        suggested_actions: ['优化任务规划', '减少任务数量', '提高专注度']
      })
    }

    // 目标进度改进
    if (goalStats.overview.averageProgress < 60) {
      opportunities.push({
        area: 'goal_progress',
        current_performance: `${goalStats.overview.averageProgress}%`,
        improvement_potential: 'medium',
        suggested_actions: ['分解大目标', '增加检查频率', '调整目标优先级']
      })
    }

    // 效率改进
    if (taskStats.trends.efficiency.level === 'low') {
      opportunities.push({
        area: 'efficiency',
        current_performance: `${taskStats.trends.efficiency.score}分`,
        improvement_potential: 'high',
        suggested_actions: ['时间块管理', '减少干扰', '优化工作流程']
      })
    }

    // 一致性改进
    const dailyStats = taskStats.timeAnalysis.dailyStats || []
    const consistency = this._calculateWeeklyConsistency(dailyStats)
    if (consistency < 60) {
      opportunities.push({
        area: 'consistency',
        current_performance: `${consistency}分`,
        improvement_potential: 'medium',
        suggested_actions: ['建立固定节奏', '设置每日提醒', '减少计划变动']
      })
    }

    return opportunities
  },

  /**
   * 生成下周行动建议
   *
   * @param {object} taskStats 任务统计
   * @param {object} goalStats 目标统计
   * @param {array} allGoals 所有目标
   * @returns {array} 行动建议数组
   */
  _generateNextWeekActions: function (taskStats, goalStats, allGoals) {
    const actions = []

    // 基于本周表现的建议
    if (taskStats.overview.completionRate >= 80) {
      actions.push({
        priority: 'medium',
        action: '保持高效节奏',
        description: '本周表现优秀，继续保持当前的工作节奏和方法',
        category: 'maintain'
      })
    } else if (taskStats.overview.completionRate < 50) {
      actions.push({
        priority: 'high',
        action: '重新规划任务量',
        description: '本周完成率较低，建议减少任务数量或延长完成时间',
        category: 'adjust'
      })
    }

    // 目标推进建议
    const stagnantGoals = allGoals.filter(goal =>
      goal.status === 'active' && (goal.progress || 0) < 20
    )

    if (stagnantGoals.length > 0) {
      actions.push({
        priority: 'high',
        action: '重点推进停滞目标',
        description: `有${stagnantGoals.length}个目标进度缓慢，下周重点关注`,
        category: 'focus'
      })
    }

    // 即将到期的目标
    const urgentGoals = allGoals.filter(goal => {
      if (!goal.dueDate || goal.status !== 'active') return false
      const dueDate = new Date(goal.dueDate)
      const today = new Date()
      const daysUntilDue = this._calculateDaysDiff(today, dueDate)
      return daysUntilDue <= 7 && daysUntilDue > 0
    })

    if (urgentGoals.length > 0) {
      actions.push({
        priority: 'critical',
        action: '紧急处理即将到期目标',
        description: `${urgentGoals.length}个目标将在一周内到期，需要优先处理`,
        category: 'urgent'
      })
    }

    return actions
  },

  /**
   * 建议下周优先级
   *
   * @param {array} allGoals 所有目标
   * @param {array} allTasks 所有任务
   * @returns {array} 优先级建议数组
   */
  _suggestNextWeekPriorities: function (allGoals, allTasks) {
    const priorities = []

    // 高优先级未完成任务
    const highPriorityTasks = allTasks.filter(task =>
      task.status !== 2 && task.priority >= 3
    )

    if (highPriorityTasks.length > 0) {
      priorities.push({
        type: 'tasks',
        priority_level: 'high',
        count: highPriorityTasks.length,
        description: '完成高优先级任务',
        items: highPriorityTasks.slice(0, 5).map(task => task.title)
      })
    }

    // 即将到期的目标
    const urgentGoals = allGoals.filter(goal => {
      if (!goal.dueDate || goal.status !== 'active') return false
      const dueDate = new Date(goal.dueDate)
      const today = new Date()
      const daysUntilDue = this._calculateDaysDiff(today, dueDate)
      return daysUntilDue <= 14 && daysUntilDue > 0
    })

    if (urgentGoals.length > 0) {
      priorities.push({
        type: 'goals',
        priority_level: 'high',
        count: urgentGoals.length,
        description: '推进即将到期的目标',
        items: urgentGoals.map(goal => goal.title)
      })
    }

    // 进度良好的目标（保持推进）
    const progressingGoals = allGoals.filter(goal =>
      goal.status === 'active' && (goal.progress || 0) >= 50 && (goal.progress || 0) < 90
    )

    if (progressingGoals.length > 0) {
      priorities.push({
        type: 'goals',
        priority_level: 'medium',
        count: progressingGoals.length,
        description: '继续推进进展良好的目标',
        items: progressingGoals.slice(0, 3).map(goal => goal.title)
      })
    }

    return priorities
  },

  /**
   * 生成优化技巧
   *
   * @param {object} taskStats 任务统计
   * @param {object} goalStats 目标统计
   * @returns {array} 优化技巧数组
   */
  _generateOptimizationTips: function (taskStats, goalStats) {
    const tips = []

    // 基于效率水平的建议
    if (taskStats.trends.efficiency.level === 'low') {
      tips.push({
        category: 'efficiency',
        tip: '使用番茄工作法',
        description: '将工作分解为25分钟的专注时间块，提高专注度和效率',
        difficulty: 'easy'
      })

      tips.push({
        category: 'efficiency',
        tip: '减少多任务处理',
        description: '专注于一次完成一个任务，避免频繁切换导致的效率损失',
        difficulty: 'medium'
      })
    }

    // 基于完成率的建议
    if (taskStats.overview.completionRate < 70) {
      tips.push({
        category: 'planning',
        tip: '采用2分钟规则',
        description: '如果一个任务可以在2分钟内完成，立即执行而不是加入待办清单',
        difficulty: 'easy'
      })

      tips.push({
        category: 'planning',
        tip: '限制每日任务数量',
        description: '每天只安排3-5个重要任务，确保有足够时间完成',
        difficulty: 'easy'
      })
    }

    // 基于目标进度的建议
    if (goalStats.overview.averageProgress < 60) {
      tips.push({
        category: 'goal_management',
        tip: '设置每周检查点',
        description: '每周回顾目标进度，及时调整策略和方法',
        difficulty: 'medium'
      })

      tips.push({
        category: 'goal_management',
        tip: '使用SMART原则',
        description: '确保目标具体、可衡量、可实现、相关性强、有时限',
        difficulty: 'medium'
      })
    }

    // 通用优化建议
    tips.push({
      category: 'general',
      tip: '建立晨间例行公事',
      description: '制定固定的晨间计划，为一天的高效工作奠定基础',
      difficulty: 'medium'
    })

    return tips
  },

  /**
   * 生成每周洞察
   *
   * @param {object} taskStats 任务统计
   * @param {object} goalStats 目标统计
   * @param {array} allGoals 所有目标
   * @returns {array} 洞察数组
   */
  _generateWeeklyInsights: function (taskStats, goalStats, allGoals) {
    const insights = []

    // 趋势洞察
    const weeklyGrowth = parseFloat(taskStats.trends.weeklyGrowth)
    if (weeklyGrowth > 20) {
      insights.push({
        type: 'trend',
        message: `任务处理量大幅增长${weeklyGrowth}%，工作强度明显提升`,
        impact: 'high'
      })
    } else if (weeklyGrowth < -20) {
      insights.push({
        type: 'trend',
        message: `任务处理量下降${Math.abs(weeklyGrowth)}%，可能需要重新激发动力`,
        impact: 'medium'
      })
    }

    // 平衡洞察
    const activeGoals = allGoals.filter(goal => goal.status === 'active')
    const typeDistribution = {}
    activeGoals.forEach(goal => {
      typeDistribution[goal.type] = (typeDistribution[goal.type] || 0) + 1
    })

    const totalGoals = activeGoals.length
    if (totalGoals > 0) {
      const habitGoals = typeDistribution.habit || 0
      const phaseGoals = typeDistribution.phase || 0

      if (habitGoals / totalGoals > 0.7) {
        insights.push({
          type: 'balance',
          message: '目标主要集中在习惯养成，可考虑增加一些阶段性成果目标',
          impact: 'low'
        })
      } else if (phaseGoals / totalGoals > 0.8) {
        insights.push({
          type: 'balance',
          message: '目标主要是阶段性的，建议加入一些长期习惯目标',
          impact: 'low'
        })
      }
    }

    // 效率洞察
    const efficiency = taskStats.trends.efficiency
    if (efficiency.level === 'high' && taskStats.overview.completionRate >= 80) {
      insights.push({
        type: 'achievement',
        message: '本周达到了高效率和高完成率的双重标准，表现卓越',
        impact: 'high'
      })
    }

    return insights
  },

  /**
   * 分析优先级分布
   *
   * @param {object} taskStats 任务统计数据
   * @returns {object} 优先级分析结果
   */
  _analyzePriorityDistribution: function (taskStats) {
    if (!taskStats || !taskStats.priority_distribution) {
      return {
        high_priority_ratio: 0,
        medium_priority_ratio: 0,
        low_priority_ratio: 0,
        priority_balance_score: 0,
        recommendations: ['无足够数据进行优先级分析']
      }
    }

    const distribution = taskStats.priority_distribution
    const total = Object.values(distribution).reduce((sum, count) => sum + count, 0)

    if (total === 0) {
      return {
        high_priority_ratio: 0,
        medium_priority_ratio: 0,
        low_priority_ratio: 0,
        priority_balance_score: 0,
        recommendations: ['暂无任务数据']
      }
    }

    const highRatio = ((distribution['5'] || 0) / total * 100).toFixed(1)
    const mediumRatio = ((distribution['3'] || 0) / total * 100).toFixed(1)
    const lowRatio = (((distribution['1'] || 0) + (distribution['0'] || 0)) / total * 100).toFixed(1)

    // 计算优先级平衡分数（理想分布：高20%，中60%，低20%）
    const idealHigh = 20, idealMedium = 60, idealLow = 20
    const deviation = Math.abs(highRatio - idealHigh) +
                     Math.abs(mediumRatio - idealMedium) +
                     Math.abs(lowRatio - idealLow)
    const balanceScore = Math.max(0, 100 - deviation / 3).toFixed(1)

    const recommendations = []
    if (highRatio > 40) {
      recommendations.push('高优先级任务过多，建议重新评估任务优先级')
    }
    if (mediumRatio < 40) {
      recommendations.push('中等优先级任务较少，建议合理分配任务优先级')
    }
    if (lowRatio > 50) {
      recommendations.push('低优先级任务过多，建议提升重要任务的优先级')
    }
    if (recommendations.length === 0) {
      recommendations.push('优先级分布合理，继续保持')
    }

    return {
      high_priority_ratio: parseFloat(highRatio),
      medium_priority_ratio: parseFloat(mediumRatio),
      low_priority_ratio: parseFloat(lowRatio),
      priority_balance_score: parseFloat(balanceScore),
      recommendations
    }
  },

  // ==================== 性能优化和缓存管理 ====================

  /**
   * 清理过期缓存
   *
   * 功能说明：
   * - 清理数据库中的过期缓存数据
   * - 释放存储空间，提升查询性能
   * - 支持按类型清理或全量清理
   *
   * @param {object} params 参数对象
   * @param {string} [params.cache_type] 缓存类型，不指定则清理所有类型
   * @returns {object} 清理结果
   */
  cleanExpiredCache: async function (params) {
    console.log('--- 调用 cleanExpiredCache ---', params)
    const { cache_type } = params || {}

    try {
      const baseApi = this._getBaseApi()
      const db = uniCloud.database()
      const collection = db.collection('dida_analytics_cache')

      let query = collection.where({
        expireTime: db.command.lt(new Date())
      })

      // 如果指定了缓存类型，添加类型过滤
      if (cache_type) {
        query = query.where({
          key: new RegExp(`^dida_analytics_${cache_type}_`)
        })
      }

      const result = await query.remove()

      const cleanupResult = {
        deleted_count: result.deleted,
        cache_type: cache_type || 'all',
        cleanup_time: new Date().toISOString()
      }

      console.log('cleanExpiredCache 成功，清理数量：', result.deleted)
      return baseApi.formatResponse(cleanupResult)
    } catch (error) {
      console.error('cleanExpiredCache 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'CLEAN_EXPIRED_CACHE_FAILED', '清理过期缓存失败')
    }
  },

  /**
   * 获取缓存统计信息
   *
   * @returns {object} 缓存统计信息
   */
  getCacheStatistics: async function () {
    console.log('--- 调用 getCacheStatistics ---')

    try {
      const baseApi = this._getBaseApi()
      const db = uniCloud.database()
      const collection = db.collection('dida_analytics_cache')

      // 获取总缓存数量
      const totalResult = await collection.count()
      const totalCount = totalResult.total

      // 获取有效缓存数量
      const validResult = await collection.where({
        expireTime: db.command.gt(new Date())
      }).count()
      const validCount = validResult.total

      // 获取过期缓存数量
      const expiredCount = totalCount - validCount

      // 按类型统计
      const allCaches = await collection.field({
        key: true,
        createTime: true,
        expireTime: true
      }).get()

      const typeStats = {}
      allCaches.data.forEach(cache => {
        const match = cache.key.match(/^dida_analytics_([^_]+)_/)
        if (match) {
          const type = match[1]
          if (!typeStats[type]) {
            typeStats[type] = { total: 0, valid: 0, expired: 0 }
          }
          typeStats[type].total++
          if (new Date(cache.expireTime) > new Date()) {
            typeStats[type].valid++
          } else {
            typeStats[type].expired++
          }
        }
      })

      const statistics = {
        overview: {
          total_caches: totalCount,
          valid_caches: validCount,
          expired_caches: expiredCount,
          cache_hit_potential: validCount > 0 ? ((validCount / totalCount) * 100).toFixed(1) : 0
        },
        by_type: typeStats,
        recommendations: this._generateCacheRecommendations(typeStats, expiredCount, totalCount)
      }

      console.log('getCacheStatistics 成功')
      return baseApi.formatResponse(statistics)
    } catch (error) {
      console.error('getCacheStatistics 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GET_CACHE_STATISTICS_FAILED', '获取缓存统计失败')
    }
  },

  /**
   * 生成缓存建议
   *
   * @param {object} typeStats 类型统计
   * @param {number} expiredCount 过期数量
   * @param {number} totalCount 总数量
   * @returns {array} 建议数组
   */
  _generateCacheRecommendations: function (typeStats, expiredCount, totalCount) {
    const recommendations = []

    // 过期缓存建议
    if (expiredCount > 0) {
      const expiredRatio = (expiredCount / totalCount) * 100
      if (expiredRatio > 30) {
        recommendations.push({
          type: 'cleanup',
          priority: 'high',
          message: `有${expiredCount}个过期缓存（${expiredRatio.toFixed(1)}%），建议立即清理`,
          action: 'cleanExpiredCache'
        })
      } else if (expiredRatio > 10) {
        recommendations.push({
          type: 'cleanup',
          priority: 'medium',
          message: `有${expiredCount}个过期缓存，建议定期清理`,
          action: 'cleanExpiredCache'
        })
      }
    }

    // 缓存效率建议
    Object.entries(typeStats).forEach(([type, stats]) => {
      const hitRate = stats.total > 0 ? (stats.valid / stats.total) * 100 : 0
      if (hitRate < 50 && stats.total > 5) {
        recommendations.push({
          type: 'optimization',
          priority: 'medium',
          message: `${type}类型缓存命中率较低（${hitRate.toFixed(1)}%），考虑调整缓存策略`,
          action: 'adjustCacheStrategy'
        })
      }
    })

    // 存储空间建议
    if (totalCount > 1000) {
      recommendations.push({
        type: 'storage',
        priority: 'low',
        message: `缓存数量较多（${totalCount}个），建议监控存储空间使用情况`,
        action: 'monitorStorage'
      })
    }

    return recommendations
  },

  /**
   * 预热缓存
   *
   * 功能说明：
   * - 预先加载常用数据到缓存
   * - 提升用户访问体验
   * - 减少首次访问延迟
   *
   * @param {object} params 参数对象
   * @param {array} [params.cache_types] 要预热的缓存类型数组
   * @returns {object} 预热结果
   */
  warmupCache: async function (params) {
    console.log('--- 调用 warmupCache ---', params)
    const { cache_types = ['goal_statistics', 'task_statistics'] } = params || {}

    try {
      const baseApi = this._getBaseApi()
      const warmupResults = []

      // 预热目标统计
      if (cache_types.includes('goal_statistics')) {
        try {
          await this.getGoalStatistics({ force_refresh: true })
          warmupResults.push({
            type: 'goal_statistics',
            status: 'success',
            message: '目标统计缓存预热成功'
          })
        } catch (error) {
          warmupResults.push({
            type: 'goal_statistics',
            status: 'failed',
            message: '目标统计缓存预热失败',
            error: error.message
          })
        }
      }

      // 预热任务统计
      if (cache_types.includes('task_statistics')) {
        try {
          await this.getTaskStatistics({ days: 30, force_refresh: true })
          warmupResults.push({
            type: 'task_statistics',
            status: 'success',
            message: '任务统计缓存预热成功'
          })
        } catch (error) {
          warmupResults.push({
            type: 'task_statistics',
            status: 'failed',
            message: '任务统计缓存预热失败',
            error: error.message
          })
        }
      }

      // 预热关键词提取
      if (cache_types.includes('task_keywords')) {
        try {
          await this.extractTaskKeywords({ limit: 20, force_refresh: true })
          warmupResults.push({
            type: 'task_keywords',
            status: 'success',
            message: '关键词提取缓存预热成功'
          })
        } catch (error) {
          warmupResults.push({
            type: 'task_keywords',
            status: 'failed',
            message: '关键词提取缓存预热失败',
            error: error.message
          })
        }
      }

      const successCount = warmupResults.filter(r => r.status === 'success').length
      const result = {
        total_types: cache_types.length,
        success_count: successCount,
        failed_count: cache_types.length - successCount,
        details: warmupResults,
        warmup_time: new Date().toISOString()
      }

      console.log('warmupCache 成功，预热类型：', successCount)
      return baseApi.formatResponse(result)
    } catch (error) {
      console.error('warmupCache 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'WARMUP_CACHE_FAILED', '预热缓存失败')
    }
  },

  /**
   * 批量数据处理优化
   *
   * @param {array} dataArray 数据数组
   * @param {function} processor 处理函数
   * @param {number} batchSize 批次大小
   * @returns {array} 处理结果
   */
  _batchProcess: async function (dataArray, processor, batchSize = 100) {
    const results = []

    for (let i = 0; i < dataArray.length; i += batchSize) {
      const batch = dataArray.slice(i, i + batchSize)
      const batchResults = await Promise.all(batch.map(processor))
      results.push(...batchResults)

      // 避免过度占用资源，添加小延迟
      if (i + batchSize < dataArray.length) {
        await new Promise(resolve => setTimeout(resolve, 10))
      }
    }

    return results
  },

  /**
   * 内存优化的数据聚合
   *
   * @param {array} data 数据数组
   * @param {string} groupBy 分组字段
   * @param {string} aggregateField 聚合字段
   * @param {string} aggregateType 聚合类型 (sum, avg, count, max, min)
   * @returns {object} 聚合结果
   */
  _optimizedAggregate: function (data, groupBy, aggregateField, aggregateType = 'sum') {
    const groups = new Map()

    data.forEach(item => {
      const key = item[groupBy]
      if (!groups.has(key)) {
        groups.set(key, { values: [], count: 0 })
      }

      const group = groups.get(key)
      group.count++

      if (aggregateField && item[aggregateField] !== undefined) {
        group.values.push(item[aggregateField])
      }
    })

    const result = {}
    groups.forEach((group, key) => {
      switch (aggregateType) {
        case 'sum':
          result[key] = group.values.reduce((sum, val) => sum + val, 0)
          break
        case 'avg':
          result[key] = group.values.length > 0 ?
            group.values.reduce((sum, val) => sum + val, 0) / group.values.length : 0
          break
        case 'count':
          result[key] = group.count
          break
        case 'max':
          result[key] = group.values.length > 0 ? Math.max(...group.values) : 0
          break
        case 'min':
          result[key] = group.values.length > 0 ? Math.min(...group.values) : 0
          break
        default:
          result[key] = group.values
      }
    })

    return result
  },

  // ==================== 测试功能 ====================

  /**
   * 数据分析功能测试
   *
   * 功能说明：
   * - 测试所有数据分析功能的正确性
   * - 验证缓存机制和性能优化
   * - 确保与 MCP 实现的兼容性
   *
   * @returns {object} 测试结果
   */
  runAnalyticsTests: async function () {
    console.log('--- 开始数据分析功能测试 ---')

    try {
      const baseApi = this._getBaseApi()
      const testResults = []
      let totalTests = 0
      let passedTests = 0

      // 测试 1: 目标统计分析
      console.log('测试 1: 目标统计分析')
      totalTests++
      try {
        const goalStatsResult = await this.getGoalStatistics({ force_refresh: true })
        if (goalStatsResult.success && goalStatsResult.data.overview) {
          testResults.push({
            test: 'getGoalStatistics',
            status: 'PASS',
            message: '目标统计分析功能正常',
            data: {
              totalGoals: goalStatsResult.data.overview.totalGoals,
              completionRate: goalStatsResult.data.overview.completionRate
            }
          })
          passedTests++
        } else {
          throw new Error('返回数据格式不正确')
        }
      } catch (error) {
        testResults.push({
          test: 'getGoalStatistics',
          status: 'FAIL',
          message: `目标统计分析测试失败: ${error.message}`,
          error: error.message
        })
      }

      // 测试 2: 任务统计分析
      console.log('测试 2: 任务统计分析')
      totalTests++
      try {
        const taskStatsResult = await this.getTaskStatistics({ days: 7, force_refresh: true })
        if (taskStatsResult.success && taskStatsResult.data.overview) {
          testResults.push({
            test: 'getTaskStatistics',
            status: 'PASS',
            message: '任务统计分析功能正常',
            data: {
              totalTasks: taskStatsResult.data.overview.totalTasks,
              completionRate: taskStatsResult.data.overview.completionRate
            }
          })
          passedTests++
        } else {
          throw new Error('返回数据格式不正确')
        }
      } catch (error) {
        testResults.push({
          test: 'getTaskStatistics',
          status: 'FAIL',
          message: `任务统计分析测试失败: ${error.message}`,
          error: error.message
        })
      }

      // 测试 3: 关键词提取
      console.log('测试 3: 关键词提取')
      totalTests++
      try {
        const keywordsResult = await this.extractTaskKeywords({ limit: 10, force_refresh: true })
        if (keywordsResult.success && keywordsResult.data.keywords) {
          testResults.push({
            test: 'extractTaskKeywords',
            status: 'PASS',
            message: '关键词提取功能正常',
            data: {
              keywordCount: Object.keys(keywordsResult.data.keywords).length,
              totalTasks: keywordsResult.data.totalTasks
            }
          })
          passedTests++
        } else {
          throw new Error('返回数据格式不正确')
        }
      } catch (error) {
        testResults.push({
          test: 'extractTaskKeywords',
          status: 'FAIL',
          message: `关键词提取测试失败: ${error.message}`,
          error: error.message
        })
      }

      // 测试 4: 获取测试目标（用于后续测试）
      console.log('测试 4: 获取测试目标')
      let testGoalId = null
      try {
        const didaGoal = uniCloud.importObject('dida-goal')
        const goalsResult = await didaGoal.getGoals({ status: 'active' })
        if (goalsResult.success && goalsResult.data.length > 0) {
          testGoalId = goalsResult.data[0].id
          console.log('找到测试目标ID:', testGoalId)
        }
      } catch (error) {
        console.log('获取测试目标失败，将跳过相关测试:', error.message)
      }

      // 测试 5: 目标进度分析（需要有效的目标ID）
      console.log('测试 5: 目标进度分析')
      totalTests++
      if (testGoalId) {
        try {
          const progressResult = await this.getGoalProgress({ goal_id: testGoalId })
          if (progressResult.success && progressResult.data.trend) {
            testResults.push({
              test: 'getGoalProgress',
              status: 'PASS',
              message: '目标进度分析功能正常',
              data: {
                goalId: testGoalId,
                trend: progressResult.data.trend.direction
              }
            })
            passedTests++
          } else {
            throw new Error('返回数据格式不正确')
          }
        } catch (error) {
          testResults.push({
            test: 'getGoalProgress',
            status: 'FAIL',
            message: `目标进度分析测试失败: ${error.message}`,
            error: error.message
          })
        }
      } else {
        testResults.push({
          test: 'getGoalProgress',
          status: 'SKIP',
          message: '跳过目标进度分析测试（无可用目标）',
          error: '没有找到可用的测试目标'
        })
      }

      // 测试 6: 目标完成预测（需要有效的目标ID）
      console.log('测试 6: 目标完成预测')
      totalTests++
      if (testGoalId) {
        try {
          const predictionResult = await this.predictGoalCompletion({ goal_id: testGoalId, force_refresh: true })
          if (predictionResult.success && predictionResult.data.prediction) {
            testResults.push({
              test: 'predictGoalCompletion',
              status: 'PASS',
              message: '目标完成预测功能正常',
              data: {
                goalId: testGoalId,
                probability: predictionResult.data.prediction.completionProbability
              }
            })
            passedTests++
          } else {
            throw new Error('返回数据格式不正确')
          }
        } catch (error) {
          testResults.push({
            test: 'predictGoalCompletion',
            status: 'FAIL',
            message: `目标完成预测测试失败: ${error.message}`,
            error: error.message
          })
        }
      } else {
        testResults.push({
          test: 'predictGoalCompletion',
          status: 'SKIP',
          message: '跳过目标完成预测测试（无可用目标）',
          error: '没有找到可用的测试目标'
        })
      }

      // 测试 7: 目标报告生成（需要有效的目标ID）
      console.log('测试 7: 目标报告生成')
      totalTests++
      if (testGoalId) {
        try {
          const reportResult = await this.generateGoalReport({ goal_id: testGoalId, force_refresh: true })
          if (reportResult.success && reportResult.data.metadata) {
            testResults.push({
              test: 'generateGoalReport',
              status: 'PASS',
              message: '目标报告生成功能正常',
              data: {
                goalId: testGoalId,
                reportType: reportResult.data.metadata.reportType
              }
            })
            passedTests++
          } else {
            throw new Error('返回数据格式不正确')
          }
        } catch (error) {
          testResults.push({
            test: 'generateGoalReport',
            status: 'FAIL',
            message: `目标报告生成测试失败: ${error.message}`,
            error: error.message
          })
        }
      } else {
        testResults.push({
          test: 'generateGoalReport',
          status: 'SKIP',
          message: '跳过目标报告生成测试（无可用目标）',
          error: '没有找到可用的测试目标'
        })
      }

      // 测试 8: 每周总结生成
      console.log('测试 8: 每周总结生成')
      totalTests++
      try {
        const summaryResult = await this.generateWeeklySummary({ force_refresh: true })
        if (summaryResult.success && summaryResult.data.metadata) {
          testResults.push({
            test: 'generateWeeklySummary',
            status: 'PASS',
            message: '每周总结生成功能正常',
            data: {
              reportType: summaryResult.data.metadata.report_type,
              weekPeriod: summaryResult.data.metadata.week_period
            }
          })
          passedTests++
        } else {
          throw new Error('返回数据格式不正确')
        }
      } catch (error) {
        testResults.push({
          test: 'generateWeeklySummary',
          status: 'FAIL',
          message: `每周总结生成测试失败: ${error.message}`,
          error: error.message
        })
      }

      // 测试 9: 缓存机制测试
      console.log('测试 9: 缓存机制测试')
      totalTests++
      try {
        const cacheStatsResult = await this.getCacheStatistics()
        if (cacheStatsResult.success && cacheStatsResult.data.overview) {
          testResults.push({
            test: 'cacheStatistics',
            status: 'PASS',
            message: '缓存机制功能正常',
            data: {
              totalCaches: cacheStatsResult.data.overview.total_caches,
              validCaches: cacheStatsResult.data.overview.valid_caches
            }
          })
          passedTests++
        } else {
          throw new Error('返回数据格式不正确')
        }
      } catch (error) {
        testResults.push({
          test: 'cacheStatistics',
          status: 'FAIL',
          message: `缓存机制测试失败: ${error.message}`,
          error: error.message
        })
      }

      // 测试 10: 性能优化测试
      console.log('测试 10: 性能优化测试')
      totalTests++
      try {
        const warmupResult = await this.warmupCache({ cache_types: ['goal_statistics', 'task_statistics'] })
        if (warmupResult.success && warmupResult.data.success_count >= 0) {
          testResults.push({
            test: 'performanceOptimization',
            status: 'PASS',
            message: '性能优化功能正常',
            data: {
              successCount: warmupResult.data.success_count,
              totalTypes: warmupResult.data.total_types
            }
          })
          passedTests++
        } else {
          throw new Error('返回数据格式不正确')
        }
      } catch (error) {
        testResults.push({
          test: 'performanceOptimization',
          status: 'FAIL',
          message: `性能优化测试失败: ${error.message}`,
          error: error.message
        })
      }

      // 计算测试结果
      const passRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0
      const testSummary = {
        total_tests: totalTests,
        passed_tests: passedTests,
        failed_tests: totalTests - passedTests,
        pass_rate: `${passRate}%`,
        test_status: passRate >= 80 ? 'EXCELLENT' : passRate >= 60 ? 'GOOD' : 'NEEDS_IMPROVEMENT',
        test_time: new Date().toISOString(),
        details: testResults
      }

      console.log(`--- 数据分析功能测试完成 ---`)
      console.log(`总测试数: ${totalTests}, 通过: ${passedTests}, 失败: ${totalTests - passedTests}`)
      console.log(`通过率: ${passRate}%`)

      return baseApi.formatResponse(testSummary)
    } catch (error) {
      console.error('数据分析功能测试失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'ANALYTICS_TESTS_FAILED', '数据分析功能测试失败')
    }
  },
}
