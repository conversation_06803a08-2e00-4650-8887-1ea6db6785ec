# 滴答清单 MCP 服务

滴答清单MCP（Memory-Context-Planning）服务是一个基于Python的后端服务，为用户提供目标管理、任务统计分析、关键词提取和任务-目标匹配等功能。该服务作为滴答清单主应用的辅助功能，帮助用户更好地规划和跟踪个人及团队目标完成情况。

## 主要功能

- **目标管理**：创建、查询、更新和删除个人目标
- **任务统计分析**：生成任务完成情况统计报告
- **关键词提取**：基于任务内容提取关键词（基于jieba分词）
- **任务与目标匹配**：智能匹配任务与相关目标
- **目标完成进度计算**：分析并可视化目标完成进度

## 安装要求

- Python 3.8+
- 滴答清单账号（支持通过token、手机号或邮箱进行认证）

## 安装步骤

1. 克隆仓库到本地

```bash
git clone https://github.com/GalaxyXieyu/didatodolist-mcp.git
cd didatodolist-mcp
```

2. 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

服务可以通过三种方式配置认证信息：

1. **配置文件**：在项目根目录创建`config.json`文件
2. **环境变量**：设置`DIDA_TOKEN`或`DIDA_PHONE`/`DIDA_EMAIL`和`DIDA_PASSWORD`
3. **命令行参数**：运行时通过参数提供认证信息

### 配置文件示例

```json
{
  "token": "你的滴答清单访问令牌",
  "phone": "你的手机号",
  "email": "你的滴答清单邮箱",
  "password": "你的滴答清单密码"
}
```

> 注意：系统优先使用token进行认证。如果提供手机号/邮箱和密码，系统将自动登录获取token并保存到配置文件中，后续将自动使用保存的token，避免频繁登录触发风控。

## 使用方法

### 基本使用

```bash
python main.py
```

### 使用命令行参数

```bash
# 使用token认证
python main.py --token "你的滴答清单token"

# 使用手机号密码认证（将获取并保存token）
python main.py --phone "13800138000" --password "yourpassword"

# 使用邮箱密码认证（将获取并保存token）
python main.py --email "<EMAIL>" --password "yourpassword"

# 指定配置文件路径
python main.py --config "custom_config.json"

# 使用SSE传输方式（而非默认的stdio）
python main.py --sse --host 127.0.0.1 --port 3000

# 注意：如果密码中包含特殊字符（如!），请使用单引号括起来
python main.py --phone "13800138000" --password 'your!password'
```

### 安装到Claude Desktop或其他MCP客户端

```bash
python main.py --install
```

## 认证机制

系统采用智能认证机制：

1. 优先使用提供的token进行认证
2. 如果没有token但提供了手机号/邮箱和密码，系统会自动登录获取token
3. 获取的token会与账号信息一起保存到配置文件，后续运行时自动使用保存的token
4. 即使用户传入相同的账号密码参数，也会优先使用已保存的token，避免频繁登录触发风控
5. 只有当传入的账号密码与配置文件中的不一致时，才会尝试使用新账号登录

## 功能模块

### 目标管理

目标管理功能允许用户创建、跟踪和管理不同类型的目标：

- **阶段性目标**：有明确截止日期的短期目标
- **常规目标**：长期持续的目标
- **习惯性目标**：需要定期执行的行为习惯

### 统计分析

统计分析功能提供多维度的任务完成情况分析：

- **时间维度**：按日/周/月分析任务完成情况
- **项目维度**：按项目分类统计任务完成率
- **标签维度**：按标签分析任务分布

### 关键词提取

基于jieba分词库，从任务内容中提取关键词，支持生成词云和热度分析。

### 任务-目标匹配

使用内容相似度和关键词匹配算法，智能关联任务与目标，帮助用户将日常任务与长期目标对齐。

## 开发历程

本项目采用了系统化的开发方法，遵循以下开发阶段：

1. **规划阶段**：定义了项目范围、功能要求和技术规范
2. **架构设计**：完成核心数据结构的设计
3. **基础功能开发**：实现核心API和数据访问层
4. **高级功能实现**：开发统计分析和目标匹配算法
5. **优化与测试**：改进性能和用户体验

## 贡献

欢迎提交问题和改进建议！请fork本仓库并创建pull request。

## 许可证

[MIT许可证](LICENSE)
