/**
 * 动态参数解析模块
 *
 * 主要功能：
 * 1. 负责解析执行步骤中的动态参数引用
 * 2. 支持步骤依赖管理和异步等待机制
 * 3. 提供灵活的参数解析和数据绑定功能
 * 4. 支持上下文数据引用和步骤结果引用
 *
 * 核心特性：
 * - 依赖管理：自动等待依赖步骤完成后再进行参数解析
 * - 动态引用：支持引用上下文数据和前置步骤的执行结果
 * - 异步解析：支持异步的参数解析和数据获取
 * - 字符串解析：智能识别和解析字符串中的动态引用
 * - 错误处理：提供完善的错误处理和异常管理
 *
 * 解析策略：
 * - 先等待依赖步骤完成，确保数据可用性
 * - 只对字符串类型的参数进行动态解析
 * - 保持非字符串参数的原始值不变
 * - 支持复杂的引用语法和表达式
 *
 * 技术架构：
 * - 异步处理：全面支持异步操作和等待机制
 * - 依赖管理：智能的步骤依赖分析和等待
 * - 类型识别：根据参数类型选择合适的解析策略
 * - 扩展性：支持自定义解析规则和引用语法
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

/**
 * 动态参数解析器
 * 负责解析执行步骤中的动态参数引用
 *
 * 核心职责：
 * - 管理步骤间的依赖关系和执行顺序
 * - 解析参数中的动态引用和表达式
 * - 从上下文和步骤结果中获取实际数据
 * - 处理解析过程中的异步操作和错误
 *
 * 解析流程：
 * 1. 等待所有依赖步骤完成执行
 * 2. 复制原始参数作为解析基础
 * 3. 遍历所有参数，识别需要解析的字符串参数
 * 4. 对每个字符串参数进行动态值解析
 * 5. 返回完全解析后的参数对象
 *
 * 设计原则：
 * - 依赖优先：确保依赖步骤完成后再进行解析
 * - 类型安全：只对字符串参数进行动态解析
 * - 异步友好：全面支持异步操作和等待
 * - 错误隔离：单个参数解析失败不影响其他参数
 */
class DynamicParameterResolver {
  static async resolveParameters(step, context) {
    const { parameters, dependencies } = step
    const resolved = { ...parameters }

    // 等待依赖步骤完成
    for (const depId of dependencies) {
      await this.waitForStepCompletion(depId, context)
    }

    // 解析动态参数
    for (const [key, value] of Object.entries(resolved)) {
      if (typeof value === 'string') {
        resolved[key] = await this.resolveDynamicValue(value, context)
      }
    }

    return resolved
  }

  static async resolveDynamicValue(value, context) {
    // 处理上下文引用：$context.key
    if (value.startsWith('$context.')) {
      const contextPath = value.substring(9)
      const [contextKey, ...pathParts] = contextPath.split('.')
      const contextValue = context.getContextData(contextKey)
      if (contextValue !== undefined) {
        // 如果有嵌套路径，继续提取
        if (pathParts.length > 0) {
          return this.extractValueByPath(contextValue, pathParts.join('.'))
        }
        return contextValue
      }
      throw new Error(`上下文数据不存在：${contextKey}`)
    }

    // 处理步骤结果引用：$step.stepId.path
    if (value.startsWith('$step.')) {
      const [, stepId, ...pathParts] = value.split('.')
      const stepResult = context.getStepResult(stepId)

      if (!stepResult) {
        throw new Error(`步骤结果不存在：${stepId}`)
      }

      return this.extractValueByPath(stepResult, pathParts.join('.'))
    }

    // 处理筛选表达式：$filter(stepId.path, condition)
    if (value.startsWith('$filter(')) {
      return this.processFilterExpression(value, context)
    }

    return value
  }

  static extractValueByPath(obj, path) {
    if (!path) return obj

    return path.split('.').reduce((current, key) => {
      // 处理数组索引：projects[0]
      const arrayMatch = key.match(/^(\w+)\[(\d+)\]$/)
      if (arrayMatch) {
        const [, arrayKey, index] = arrayMatch
        return current?.[arrayKey]?.[parseInt(index)]
      }

      // 处理数组筛选：projects[name=okr]
      const filterMatch = key.match(/^(\w+)\[(\w+)=(.+)\]$/)
      if (filterMatch) {
        const [, arrayKey, filterKey, filterValue] = filterMatch
        const array = current?.[arrayKey]
        if (Array.isArray(array)) {
          return array.find((item) => item[filterKey]?.toLowerCase().includes(filterValue.toLowerCase()))
        }
      }

      return current?.[key]
    }, obj)
  }

  static async processFilterExpression(expression, context) {
    // 解析筛选表达式：$filter(step1.data, name contains "okr")
    const match = expression.match(/\$filter\(([^,]+),\s*(.+)\)/)
    if (!match) {
      throw new Error(`无效的筛选表达式：${expression}`)
    }

    const [, dataPath, condition] = match

    // 处理步骤结果引用
    if (dataPath.startsWith('step')) {
      const [stepId, ...pathParts] = dataPath.split('.')
      const stepResult = context.getStepResult(stepId)

      if (!stepResult) {
        throw new Error(`步骤结果不存在：${stepId}`)
      }

      const data = this.extractValueByPath(stepResult, pathParts.join('.'))

      if (!Array.isArray(data)) {
        throw new Error(`筛选目标必须是数组：${dataPath}`)
      }

      return this.applyFilter(data, condition)
    } else {
      // 其他类型的数据路径
      const data = await this.resolveDynamicValue(`$${dataPath}`, context)

      if (!Array.isArray(data)) {
        throw new Error(`筛选目标必须是数组：${dataPath}`)
      }

      return this.applyFilter(data, condition)
    }
  }

  static applyFilter(array, condition) {
    // 解析条件：name contains "okr"
    const conditionMatch = condition.match(/(\w+)\s+(contains|equals|startsWith)\s+"([^"]+)"/)
    if (!conditionMatch) {
      throw new Error(`无效的筛选条件：${condition}`)
    }

    const [, field, operator, value] = conditionMatch

    return array.filter((item) => {
      const fieldValue = item[field]?.toString().toLowerCase() || ''
      const searchValue = value.toLowerCase()

      switch (operator) {
        case 'contains':
          return fieldValue.includes(searchValue)
        case 'equals':
          return fieldValue === searchValue
        case 'startsWith':
          return fieldValue.startsWith(searchValue)
        default:
          return false
      }
    })
  }

  static async waitForStepCompletion(stepId, context) {
    // 等待依赖步骤完成的逻辑
    return new Promise((resolve) => {
      const checkCompletion = () => {
        const stepResult = context.getStepResult(stepId)
        if (stepResult !== undefined) {
          resolve()
        } else {
          setTimeout(checkCompletion, 100)
        }
      }
      checkCompletion()
    })
  }
}

module.exports = {
  DynamicParameterResolver,
}
