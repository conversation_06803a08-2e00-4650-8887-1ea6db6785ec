/**
 * dida-goal 云函数 - 滴答清单目标管理
 * 
 * 主要功能：
 * 1. 目标 CRUD 操作：创建、查询、更新、删除目标
 * 2. 任务目标匹配：智能匹配任务与目标的关联关系
 * 3. 目标数据管理：基于任务系统的目标元数据存储
 * 4. API 兼容性：与 MCP 实现保持完全兼容
 * 
 * 技术特性：
 * - 集成 dida-base 云函数的统一 API 封装
 * - 统一的错误处理和响应格式
 * - 完整的参数验证和边界条件处理
 * - 支持按 ID 或关键词查找目标
 * - 智能的任务目标匹配算法
 * - 基于任务系统的目标数据存储
 * 
 * API 兼容性：
 * - 完全兼容 MCP didatodolist-mcp/tools/goal_tools.py 实现
 * - 保持相同的参数格式和响应结构
 * - 支持所有 MCP 目标管理功能
 * 
 * 目标数据模型：
 * - 目标作为特殊任务存储在"目标管理"项目中
 * - 使用任务的 content 字段存储目标的 JSON 元数据
 * - 支持阶段性目标、永久目标、习惯目标三种类型
 * - 通过关键词匹配实现任务与目标的关联
 * 
 * <AUTHOR> 开发团队
 * @version 1.0.0
 * @since 2024-01-01
 */

module.exports = {
  /**
   * 获取 dida-base 云函数实例
   * 用于统一的 API 请求封装和错误处理
   * 
   * @returns {object} dida-base 云函数实例
   */
  _getBaseApi: function () {
    return uniCloud.importObject('dida-base')
  },

  /**
   * 获取或创建目标管理项目
   * 目标作为特殊任务存储在专门的"目标管理"项目中
   * 
   * @returns {object} 目标管理项目信息
   */
  _getGoalProject: async function () {
    try {
      const baseApi = this._getBaseApi()
      
      // 获取所有项目
      const response = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
      if (!response.success) {
        throw new Error('获取项目列表失败')
      }

      const projects = response.data.projectProfiles || []
      
      // 查找目标管理项目
      let goalProject = projects.find((p) => p.name === '目标管理')
      
      // 如果不存在，创建目标管理项目
      if (!goalProject) {
        const createResponse = await baseApi.makeRequest('/api/v2/project', 'POST', {
          name: '目标管理',
          color: '#9C27B0', // 紫色，表示目标管理
        })
        
        if (!createResponse.success) {
          throw new Error('创建目标管理项目失败')
        }
        
        goalProject = createResponse.data
      }

      return goalProject
    } catch (error) {
      console.error('获取目标管理项目失败：', error)
      throw error
    }
  },

  // ==================== 目标 CRUD 操作 ====================

  /**
   * 创建新目标
   * 
   * 功能说明：
   * - 创建新的目标，作为特殊任务存储在目标管理项目中
   * - 支持阶段性目标、永久目标、习惯目标三种类型
   * - 目标元数据存储在任务的 content 字段中
   * 
   * @param {object} params 参数对象
   * @param {string} params.title 目标标题（必需）
   * @param {string} params.type 目标类型：phase/permanent/habit（必需）
   * @param {string} params.keywords 关键词，以逗号分隔（必需）
   * @param {string} [params.description] 目标的基础描述
   * @param {string} [params.due_date] 截止日期 (YYYY-MM-DD)，阶段性目标必填
   * @param {string} [params.start_date] 开始日期 (YYYY-MM-DD)
   * @param {string} [params.frequency] 频率 (daily, weekly:1,3,5 等)，习惯目标必填
   * @param {array} [params.related_projects] 相关项目IDs
   * @returns {object} 创建的目标信息
   */
  createGoal: async function (params) {
    console.log('--- 调用 createGoal ---', params)
    const { title, type, keywords, description, due_date, start_date, frequency, related_projects } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!title) {
        return baseApi.handleError(new Error('目标标题不能为空'), 'GOAL_TITLE_REQUIRED', '目标标题不能为空')
      }

      if (!type || !['phase', 'permanent', 'habit'].includes(type)) {
        return baseApi.handleError(
          new Error('目标类型必须是 phase、permanent 或 habit'),
          'INVALID_GOAL_TYPE',
          '目标类型必须是 phase、permanent 或 habit'
        )
      }

      if (!keywords) {
        return baseApi.handleError(new Error('关键词不能为空'), 'GOAL_KEYWORDS_REQUIRED', '关键词不能为空')
      }

      // 阶段性目标必须有截止日期
      if (type === 'phase' && !due_date) {
        return baseApi.handleError(
          new Error('阶段性目标必须设置截止日期'),
          'PHASE_GOAL_DUE_DATE_REQUIRED',
          '阶段性目标必须设置截止日期'
        )
      }

      // 习惯目标必须有频率
      if (type === 'habit' && !frequency) {
        return baseApi.handleError(
          new Error('习惯目标必须设置频率'),
          'HABIT_GOAL_FREQUENCY_REQUIRED',
          '习惯目标必须设置频率'
        )
      }

      // 获取目标管理项目
      const goalProject = await this._getGoalProject()

      // 构建目标元数据
      const goalMetadata = {
        type,
        keywords: keywords.split(',').map((k) => k.trim()),
        description: description || '',
        status: 'active',
        progress: 0,
        createdTime: new Date().toISOString(),
      }

      if (due_date) goalMetadata.dueDate = due_date
      if (start_date) goalMetadata.startDate = start_date
      if (frequency) goalMetadata.frequency = frequency
      if (related_projects) goalMetadata.relatedProjects = related_projects

      // 创建目标任务
      const taskData = {
        title: title,
        content: JSON.stringify(goalMetadata),
        projectId: goalProject.id,
        priority: 3, // 中等优先级
        status: 0, // 未完成状态
      }

      if (due_date) {
        taskData.dueDate = due_date + 'T23:59:59.000Z'
      }

      if (start_date) {
        taskData.startDate = start_date + 'T00:00:00.000Z'
      }

      // 创建任务
      const response = await baseApi.makeRequest(`/api/v2/project/${goalProject.id}/task`, 'POST', taskData)
      if (!response.success) {
        return response
      }

      console.log('createGoal 成功，目标ID：', response.data.id)
      return response
    } catch (error) {
      console.error('createGoal 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'CREATE_GOAL_FAILED', '创建目标失败')
    }
  },

  /**
   * 获取目标列表
   * 
   * 功能说明：
   * - 获取用户的所有目标
   * - 支持按类型、状态、关键词筛选
   * - 返回标准化的目标数据格式
   * 
   * @param {object} params 参数对象
   * @param {string} [params.type] 目标类型筛选 (phase/permanent/habit)
   * @param {string} [params.status] 目标状态筛选 (active/completed)
   * @param {string} [params.keywords] 关键词筛选（匹配目标标题或关键词）
   * @returns {object} 目标列表响应
   */
  getGoals: async function (params) {
    console.log('--- 调用 getGoals ---', params)
    const { type, status, keywords } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 获取目标管理项目
      const goalProject = await this._getGoalProject()

      // 获取所有任务
      const response = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
      if (!response.success) {
        return response
      }

      const allTasks = response.data.syncTaskBean?.update || []

      // 筛选目标管理项目中的任务
      let goalTasks = allTasks.filter((task) => task.projectId === goalProject.id)

      // 转换为目标格式并应用筛选
      const goals = goalTasks
        .map((task) => {
          try {
            const metadata = JSON.parse(task.content || '{}')
            return {
              id: task.id,
              title: task.title,
              type: metadata.type || 'permanent',
              status: task.status === 2 ? 'completed' : 'active',
              keywords: metadata.keywords || [],
              description: metadata.description || '',
              progress: metadata.progress || 0,
              dueDate: metadata.dueDate || null,
              startDate: metadata.startDate || null,
              frequency: metadata.frequency || null,
              relatedProjects: metadata.relatedProjects || [],
              createdTime: metadata.createdTime || task.createdTime,
              modifiedTime: task.modifiedTime,
            }
          } catch (error) {
            console.warn('解析目标元数据失败：', task.id, error)
            return null
          }
        })
        .filter((goal) => goal !== null)

      // 应用筛选条件
      let filteredGoals = goals

      if (type) {
        filteredGoals = filteredGoals.filter((goal) => goal.type === type)
      }

      if (status) {
        filteredGoals = filteredGoals.filter((goal) => goal.status === status)
      }

      if (keywords) {
        const searchKeywords = keywords.toLowerCase()
        filteredGoals = filteredGoals.filter(
          (goal) =>
            goal.title.toLowerCase().includes(searchKeywords) ||
            goal.keywords.some((k) => k.toLowerCase().includes(searchKeywords))
        )
      }

      console.log('getGoals 成功，返回目标数量：', filteredGoals.length)
      return baseApi.formatResponse(filteredGoals)
    } catch (error) {
      console.error('getGoals 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GET_GOALS_FAILED', '获取目标列表失败')
    }
  },

  /**
   * 获取目标详情
   * 
   * 功能说明：
   * - 获取指定目标的详细信息
   * - 支持按目标ID查找
   * 
   * @param {object} params 参数对象
   * @param {string} params.goal_id 目标ID（任务ID）（必需）
   * @returns {object} 目标详情
   */
  getGoal: async function (params) {
    console.log('--- 调用 getGoal ---', params)
    const { goal_id } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!goal_id) {
        return baseApi.handleError(new Error('目标ID不能为空'), 'GOAL_ID_REQUIRED', '目标ID不能为空')
      }

      // 查找目标
      const goal = await this._findGoalById(goal_id)
      if (!goal) {
        return baseApi.handleError(new Error('目标不存在'), 'GOAL_NOT_FOUND', '目标不存在')
      }

      console.log('getGoal 成功，目标ID：', goal_id)
      return baseApi.formatResponse(goal)
    } catch (error) {
      console.error('getGoal 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GET_GOAL_FAILED', '获取目标详情失败')
    }
  },

  /**
   * 更新目标
   *
   * 功能说明：
   * - 更新现有目标的信息
   * - 支持部分字段更新
   * - 自动更新目标元数据
   *
   * @param {object} params 参数对象
   * @param {string} params.goal_id 目标ID（任务ID）（必需）
   * @param {string} [params.title] 新标题
   * @param {string} [params.type] 新类型 (phase/permanent/habit)
   * @param {string} [params.status] 新状态 (active/completed)
   * @param {string} [params.keywords] 新关键词（逗号分隔）
   * @param {string} [params.description] 新的基础描述
   * @param {string} [params.due_date] 新截止日期 (YYYY-MM-DD)
   * @param {string} [params.start_date] 新开始日期 (YYYY-MM-DD)
   * @param {string} [params.frequency] 新频率
   * @param {number} [params.progress] 进度（忽略）
   * @param {array} [params.related_projects] 相关项目（忽略）
   * @returns {object} 更新后的目标数据
   */
  updateGoal: async function (params) {
    console.log('--- 调用 updateGoal ---', params)
    const { goal_id, title, type, status, keywords, description, due_date, start_date, frequency } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!goal_id) {
        return baseApi.handleError(new Error('目标ID不能为空'), 'GOAL_ID_REQUIRED', '目标ID不能为空')
      }

      // 查找目标
      const existingGoal = await this._findGoalById(goal_id)
      if (!existingGoal) {
        return baseApi.handleError(new Error('目标不存在'), 'GOAL_NOT_FOUND', '目标不存在')
      }

      // 验证目标类型
      if (type && !['phase', 'permanent', 'habit'].includes(type)) {
        return baseApi.handleError(
          new Error('目标类型必须是 phase、permanent 或 habit'),
          'INVALID_GOAL_TYPE',
          '目标类型必须是 phase、permanent 或 habit'
        )
      }

      // 构建更新的元数据
      const currentMetadata = JSON.parse(existingGoal.content || '{}')
      const updatedMetadata = { ...currentMetadata }

      if (type !== undefined) updatedMetadata.type = type
      if (status !== undefined) updatedMetadata.status = status
      if (keywords !== undefined) updatedMetadata.keywords = keywords.split(',').map((k) => k.trim())
      if (description !== undefined) updatedMetadata.description = description
      if (due_date !== undefined) updatedMetadata.dueDate = due_date
      if (start_date !== undefined) updatedMetadata.startDate = start_date
      if (frequency !== undefined) updatedMetadata.frequency = frequency

      updatedMetadata.modifiedTime = new Date().toISOString()

      // 构建任务更新数据
      const updateData = {
        id: goal_id,
        projectId: existingGoal.projectId,
        content: JSON.stringify(updatedMetadata),
      }

      if (title !== undefined) updateData.title = title
      if (status !== undefined) updateData.status = status === 'completed' ? 2 : 0

      if (due_date !== undefined) {
        updateData.dueDate = due_date ? due_date + 'T23:59:59.000Z' : null
      }

      if (start_date !== undefined) {
        updateData.startDate = start_date ? start_date + 'T00:00:00.000Z' : null
      }

      // 更新任务
      const response = await baseApi.makeRequest(`/api/v2/project/${existingGoal.projectId}/task/${goal_id}`, 'POST', updateData)
      if (!response.success) {
        return response
      }

      console.log('updateGoal 成功，目标ID：', goal_id)
      return response
    } catch (error) {
      console.error('updateGoal 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'UPDATE_GOAL_FAILED', '更新目标失败')
    }
  },

  /**
   * 删除目标
   *
   * 功能说明：
   * - 删除指定的目标
   * - 实际上是删除对应的任务
   *
   * @param {object} params 参数对象
   * @param {string} params.goal_id 目标ID（任务ID）（必需）
   * @returns {object} 删除操作的结果
   */
  deleteGoal: async function (params) {
    console.log('--- 调用 deleteGoal ---', params)
    const { goal_id } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!goal_id) {
        return baseApi.handleError(new Error('目标ID不能为空'), 'GOAL_ID_REQUIRED', '目标ID不能为空')
      }

      // 查找目标
      const goal = await this._findGoalById(goal_id)
      if (!goal) {
        return baseApi.handleError(new Error('目标不存在'), 'GOAL_NOT_FOUND', '目标不存在')
      }

      // 删除任务
      const response = await baseApi.makeRequest(`/api/v2/project/${goal.projectId}/task/${goal_id}`, 'DELETE')
      if (!response.success) {
        return response
      }

      console.log('deleteGoal 成功，目标ID：', goal_id)
      return response
    } catch (error) {
      console.error('deleteGoal 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'DELETE_GOAL_FAILED', '删除目标失败')
    }
  },

  // ==================== 任务目标匹配功能 ====================

  /**
   * 匹配任务与目标
   *
   * 功能说明：
   * - 根据任务内容智能匹配相关目标
   * - 基于关键词匹配算法
   * - 返回匹配度排序的目标列表
   *
   * @param {object} params 参数对象
   * @param {string} params.task_title 任务标题（必需）
   * @param {string} [params.task_content] 任务内容
   * @param {string} [params.project_id] 任务所属项目ID
   * @returns {object} 匹配的目标列表（按匹配度排序）
   */
  matchTaskWithGoals: async function (params) {
    console.log('--- 调用 matchTaskWithGoals ---', params)
    const { task_title, task_content, project_id } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!task_title) {
        return baseApi.handleError(new Error('任务标题不能为空'), 'TASK_TITLE_REQUIRED', '任务标题不能为空')
      }

      // 获取所有活跃目标
      const goalsResponse = await this.getGoals({ status: 'active' })
      if (!goalsResponse.success) {
        return goalsResponse
      }

      const goals = goalsResponse.data

      // 构建任务文本（标题 + 内容）
      const taskText = (task_title + ' ' + (task_content || '')).toLowerCase()

      // 计算每个目标的匹配度
      const matches = goals
        .map((goal) => {
          let score = 0
          let matchedKeywords = []

          // 关键词匹配
          goal.keywords.forEach((keyword) => {
            if (taskText.includes(keyword.toLowerCase())) {
              score += 10 // 关键词匹配得分
              matchedKeywords.push(keyword)
            }
          })

          // 目标标题匹配
          const goalTitleWords = goal.title.toLowerCase().split(' ')
          goalTitleWords.forEach((word) => {
            if (word.length > 2 && taskText.includes(word)) {
              score += 5 // 标题词匹配得分
            }
          })

          // 项目关联匹配
          if (project_id && goal.relatedProjects && goal.relatedProjects.includes(project_id)) {
            score += 15 // 项目关联得分
          }

          return {
            goal,
            score,
            matchedKeywords,
            matchReason: this._generateMatchReason(score, matchedKeywords, goal),
          }
        })
        .filter((match) => match.score > 0) // 只返回有匹配的目标
        .sort((a, b) => b.score - a.score) // 按匹配度降序排列

      console.log('matchTaskWithGoals 成功，匹配目标数量：', matches.length)
      return baseApi.formatResponse(matches)
    } catch (error) {
      console.error('matchTaskWithGoals 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'MATCH_TASK_WITH_GOALS_FAILED', '匹配任务与目标失败')
    }
  },

  // ==================== 辅助方法 ====================

  /**
   * 根据ID查找目标
   *
   * @param {string} goalId 目标ID
   * @returns {object|null} 找到的目标对象，未找到返回null
   */
  _findGoalById: async function (goalId) {
    try {
      const baseApi = this._getBaseApi()

      // 获取目标管理项目
      const goalProject = await this._getGoalProject()

      // 获取所有任务
      const response = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
      if (!response.success) {
        return null
      }

      const allTasks = response.data.syncTaskBean?.update || []

      // 查找指定的目标任务
      const goalTask = allTasks.find((task) => task.id === goalId && task.projectId === goalProject.id)

      if (!goalTask) {
        return null
      }

      // 解析目标元数据
      try {
        const metadata = JSON.parse(goalTask.content || '{}')
        return {
          ...goalTask,
          metadata,
        }
      } catch (error) {
        console.warn('解析目标元数据失败：', goalId, error)
        return goalTask
      }
    } catch (error) {
      console.error('查找目标失败：', error)
      return null
    }
  },

  /**
   * 生成匹配原因说明
   *
   * @param {number} score 匹配得分
   * @param {array} matchedKeywords 匹配的关键词
   * @param {object} goal 目标对象
   * @returns {string} 匹配原因说明
   */
  _generateMatchReason: function (score, matchedKeywords, goal) {
    const reasons = []

    if (matchedKeywords.length > 0) {
      reasons.push(`关键词匹配: ${matchedKeywords.join(', ')}`)
    }

    if (score >= 15) {
      reasons.push('项目关联匹配')
    }

    if (score >= 5 && matchedKeywords.length === 0) {
      reasons.push('标题相似度匹配')
    }

    return reasons.length > 0 ? reasons.join('; ') : '智能匹配'
  },
}
