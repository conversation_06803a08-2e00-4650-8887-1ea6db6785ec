/**
 * 滴答清单任务和项目管理云函数
 * 重构版本 - 使用 dida-base 云函数提供的统一 API 封装
 *
 * 功能模块：
 * - 任务管理：getTasks, createTask, updateTask, deleteTask
 * - 项目管理：getProjects, getProject, createProject, updateProject, deleteProject
 *
 * 技术特性：
 * - 使用 dida-base 云函数的统一请求封装
 * - 标准化错误处理和响应格式
 * - 与 MCP 实现 100% API 兼容
 */

module.exports = {
  _before: function () {
    // 通用预处理器
    console.log('dida-todo 云函数调用开始')
  },

  /**
   * 获取 dida-base 云函数实例
   * @returns {object} dida-base 云函数实例
   */
  _getBaseApi: function () {
    return uniCloud.importObject('dida-base')
  },

  // ==================== 任务管理功能 ====================

  /**
   * 获取任务列表
   * @param {object} params 参数对象
   * @param {string} [params.mode="all"] 任务模式，支持 'all'(所有), 'today'(今天), 'yesterday'(昨天), 'recent_7_days'(最近7天)
   * @param {string} [params.keyword] 关键词筛选
   * @param {number} [params.priority] 优先级筛选 (0-最低, 1-低, 3-中, 5-高)
   * @param {string} [params.project_name] 项目名称筛选
   * @param {boolean} [params.completed] 是否已完成，true表示已完成，false表示未完成，null表示全部
   * @returns {object} 符合条件的任务列表
   */
  getTasks: async function (params) {
    console.log('--- 调用 getTasks ---', params)
    const { mode = 'all', keyword, priority, project_name, completed } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 获取所有项目和任务数据
      const batchResponse = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
      if (!batchResponse.success) {
        return batchResponse
      }

      const batchData = batchResponse.data
      const projectProfiles = batchData.projectProfiles || []
      const syncTaskBean = batchData.syncTaskBean || {}
      const allTasks = syncTaskBean.update || []

      // 构建项目映射
      const projectMap = {}
      projectProfiles.forEach((project) => {
        projectMap[project.id] = project.name
      })

      // 为任务添加项目名称
      allTasks.forEach((task) => {
        if (task.projectId && !task.projectName) {
          task.projectName = projectMap[task.projectId] || '未知项目'
        }
      })

      // 应用筛选条件
      let filteredTasks = allTasks

      // 完成状态筛选
      if (completed !== undefined && completed !== null) {
        filteredTasks = filteredTasks.filter((task) => {
          const isCompleted = task.status === 2
          return completed ? isCompleted : !isCompleted
        })
      }

      // 项目名称筛选
      if (project_name) {
        filteredTasks = filteredTasks.filter((task) => {
          return task.projectName && task.projectName.toLowerCase().includes(project_name.toLowerCase())
        })
      }

      // 优先级筛选
      if (priority !== undefined && priority !== null) {
        filteredTasks = filteredTasks.filter((task) => task.priority === priority)
      }

      // 关键词筛选
      if (keyword) {
        const keywordLower = keyword.toLowerCase()
        filteredTasks = filteredTasks.filter((task) => {
          return (
            (task.title && task.title.toLowerCase().includes(keywordLower)) ||
            (task.content && task.content.toLowerCase().includes(keywordLower))
          )
        })
      }

      // 时间模式筛选
      if (mode && mode !== 'all') {
        const now = new Date()
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
        const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

        filteredTasks = filteredTasks.filter((task) => {
          if (!task.dueDate) return false

          const taskDate = new Date(task.dueDate)
          const taskDay = new Date(taskDate.getFullYear(), taskDate.getMonth(), taskDate.getDate())

          switch (mode) {
            case 'today':
              return taskDay.getTime() === today.getTime()
            case 'yesterday':
              return taskDay.getTime() === yesterday.getTime()
            case 'recent_7_days':
              return taskDay.getTime() >= sevenDaysAgo.getTime() && taskDay.getTime() <= today.getTime()
            default:
              return true
          }
        })
      }

      console.log('getTasks 成功，返回任务数量：', filteredTasks.length)

      return baseApi.formatResponse(filteredTasks)
    } catch (error) {
      console.error('getTasks 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GET_TASKS_FAILED', '获取任务列表失败')
    }
  },

  /**
   * 创建新任务
   * @param {object} params 参数对象
   * @param {string} [params.title] 任务标题
   * @param {string} [params.content] 任务内容
   * @param {number} [params.priority] 优先级 (0-最低, 1-低, 3-中, 5-高)
   * @param {string} [params.project_name] 项目名称
   * @param {Array<string>} [params.tag_names] 标签名称列表
   * @param {string} [params.start_date] 开始日期，格式 'YYYY-MM-DD HH:MM:SS'
   * @param {string} [params.due_date] 截止日期，格式 'YYYY-MM-DD HH:MM:SS'
   * @param {boolean} [params.is_all_day] 是否为全天任务
   * @param {string} [params.reminder] 提醒选项，如 "0"(准时), "-5M"(提前5分钟), "-1H"(提前1小时), "-1D"(提前1天)
   * @returns {object} 创建的任务信息
   */
  createTask: async function (params) {
    console.log('--- 调用 createTask ---', params)
    const { title, content, priority, project_name, tag_names, start_date, due_date, is_all_day, reminder } =
      params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!title) {
        return baseApi.handleError(new Error('任务标题不能为空'), 'TITLE_REQUIRED', '任务标题不能为空')
      }

      // 获取项目信息
      let project_id = null
      if (project_name) {
        const projectsResponse = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
        if (!projectsResponse.success) {
          return projectsResponse
        }

        const projects = projectsResponse.data.projectProfiles || []

        // 精确匹配项目名称
        const project =
          projects.find((p) => p.name === project_name) ||
          projects.find((p) => p.name.toLowerCase() === project_name.toLowerCase()) ||
          projects.find((p) => p.name.toLowerCase().includes(project_name.toLowerCase()))

        if (project) {
          project_id = project.id
        } else {
          console.warn(`未找到项目: ${project_name}，将使用默认项目`)
        }
      }

      // 构建任务数据
      const taskData = {
        title: title,
        content: content || '',
        priority: priority || 0,
        projectId: project_id,
      }

      // 处理日期时间
      if (start_date) {
        taskData.startDate = this._formatDateForAPI(start_date)
      }

      if (due_date) {
        taskData.dueDate = this._formatDateForAPI(due_date)
      }

      if (is_all_day !== undefined) {
        taskData.isAllDay = is_all_day
      }

      if (reminder) {
        taskData.reminders = [reminder]
      }

      // 创建任务
      const response = await baseApi.makeRequest('/api/v2/task', 'POST', taskData)
      if (!response.success) {
        return response
      }

      console.log('createTask 成功，任务ID：', response.data.id)
      return response
    } catch (error) {
      console.error('createTask 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'CREATE_TASK_FAILED', '创建任务失败')
    }
  },

  /**
   * 更新任务
   * @param {object} params 参数对象
   * @param {string} params.task_id_or_title 任务ID或任务标题
   * @param {string} [params.title] 新任务标题
   * @param {string} [params.content] 新任务内容
   * @param {number} [params.priority] 新优先级 (0-最低, 1-低, 3-中, 5-高)
   * @param {string} [params.project_name] 新项目名称
   * @param {Array<string>} [params.tag_names] 新标签名称列表
   * @param {string} [params.start_date] 新开始日期，格式 'YYYY-MM-DD HH:MM:SS'
   * @param {string} [params.due_date] 新截止日期，格式 'YYYY-MM-DD HH:MM:SS'
   * @param {boolean} [params.is_all_day] 是否为全天任务
   * @param {string} [params.reminder] 新提醒选项
   * @param {number} [params.status] 新状态，0表示未完成，2表示已完成
   * @returns {object} 更新后的任务信息
   */
  updateTask: async function (params) {
    console.log('--- 调用 updateTask ---', params)
    const {
      task_id_or_title,
      title,
      content,
      priority,
      project_name,
      tag_names,
      start_date,
      due_date,
      is_all_day,
      reminder,
      status,
    } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!task_id_or_title) {
        return baseApi.handleError(new Error('任务ID或标题不能为空'), 'TASK_ID_REQUIRED', '任务ID或标题不能为空')
      }

      // 查找任务
      const task = await this._findTaskByIdOrTitle(task_id_or_title)
      if (!task) {
        return baseApi.handleError(new Error('任务不存在'), 'TASK_NOT_FOUND', '任务不存在')
      }

      // 构建更新数据
      const updateData = {
        id: task.id,
        projectId: task.projectId,
      }

      // 更新字段
      if (title !== undefined) updateData.title = title
      if (content !== undefined) updateData.content = content
      if (priority !== undefined) updateData.priority = priority
      if (status !== undefined) updateData.status = status

      // 处理项目名称
      if (project_name) {
        const projectsResponse = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
        if (projectsResponse.success) {
          const projects = projectsResponse.data.projectProfiles || []
          const project =
            projects.find((p) => p.name === project_name) ||
            projects.find((p) => p.name.toLowerCase() === project_name.toLowerCase())

          if (project) {
            updateData.projectId = project.id
          }
        }
      }

      // 处理日期时间
      if (start_date !== undefined) {
        updateData.startDate = start_date ? this._formatDateForAPI(start_date) : null
      }

      if (due_date !== undefined) {
        updateData.dueDate = due_date ? this._formatDateForAPI(due_date) : null
      }

      if (is_all_day !== undefined) {
        updateData.isAllDay = is_all_day
      }

      if (reminder !== undefined) {
        updateData.reminders = reminder ? [reminder] : []
      }

      // 更新任务
      const response = await baseApi.makeRequest(`/api/v2/task/${task.id}`, 'POST', updateData)
      if (!response.success) {
        return response
      }

      console.log('updateTask 成功，任务ID：', task.id)
      return response
    } catch (error) {
      console.error('updateTask 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'UPDATE_TASK_FAILED', '更新任务失败')
    }
  },

  /**
   * 删除任务
   * @param {object} params 参数对象
   * @param {string} params.task_id_or_title 任务ID或任务标题
   * @returns {object} 删除操作的响应
   */
  deleteTask: async function (params) {
    console.log('--- 调用 deleteTask ---', params)
    const { task_id_or_title } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!task_id_or_title) {
        return baseApi.handleError(new Error('任务ID或标题不能为空'), 'TASK_ID_REQUIRED', '任务ID或标题不能为空')
      }

      // 查找任务
      const task = await this._findTaskByIdOrTitle(task_id_or_title)
      if (!task) {
        return baseApi.handleError(new Error('任务不存在'), 'TASK_NOT_FOUND', '任务不存在')
      }

      // 删除任务
      const response = await baseApi.makeRequest(`/api/v2/project/${task.projectId}/task/${task.id}`, 'DELETE')
      if (!response.success) {
        return response
      }

      console.log('deleteTask 成功，任务ID：', task.id)
      return response
    } catch (error) {
      console.error('deleteTask 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'DELETE_TASK_FAILED', '删除任务失败')
    }
  },

  /**
   * 根据ID或标题查找任务
   * @param {string} idOrTitle 任务ID或标题
   * @returns {object|null} 找到的任务对象，未找到返回null
   */
  _findTaskByIdOrTitle: async function (idOrTitle) {
    try {
      const baseApi = this._getBaseApi()

      // 获取所有任务
      const batchResponse = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
      if (!batchResponse.success) {
        return null
      }

      const allTasks = batchResponse.data.syncTaskBean?.update || []

      // 首先尝试按ID查找
      let task = allTasks.find((t) => t.id === idOrTitle)

      // 如果按ID未找到，尝试按标题查找
      if (!task) {
        task = allTasks.find((t) => t.title === idOrTitle)
      }

      return task || null
    } catch (error) {
      console.error('查找任务失败：', error)
      return null
    }
  },

  /**
   * 格式化日期为 API 所需格式
   * @param {string} dateStr 日期字符串，格式 'YYYY-MM-DD HH:MM:SS'
   * @returns {string} API 格式的日期字符串
   */
  _formatDateForAPI: function (dateStr) {
    try {
      // 解析输入的日期字符串
      const date = new Date(dateStr)
      if (isNaN(date.getTime())) {
        throw new Error('无效的日期格式')
      }

      // 转换为 UTC 时间并格式化为 ISO 字符串
      return date.toISOString()
    } catch (error) {
      console.error('日期格式化失败：', error)
      return dateStr // 返回原始字符串
    }
  },

  // ==================== 项目管理功能 ====================

  /**
   * 获取所有项目列表
   * @returns {object} 项目列表
   */
  getProjects: async function () {
    console.log('--- 调用 getProjects ---')

    try {
      const baseApi = this._getBaseApi()

      // 获取项目数据
      const response = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
      if (!response.success) {
        return response
      }

      const projectProfiles = response.data.projectProfiles || []

      // 转换为标准格式
      const result = projectProfiles
        .map((project) => ({
          id: project.id,
          name: project.name,
          color: project.color,
          sortOrder: project.sortOrder,
          sortType: project.sortType,
          modifiedTime: project.modifiedTime,
          description: project.description || null,
          isArchived: project.isArchived || false,
        }))
        .filter((project) => project.id && project.name) // 过滤无效项目

      console.log('getProjects 成功，返回项目数量：', result.length)
      return baseApi.formatResponse(result)
    } catch (error) {
      console.error('getProjects 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GET_PROJECTS_FAILED', '获取项目列表失败')
    }
  },

  /**
   * 获取项目详情
   * @param {object} params 参数对象
   * @param {string} params.project_id_or_name 项目ID或项目名称
   * @returns {object} 项目详情
   */
  getProject: async function (params) {
    console.log('--- 调用 getProject ---', params)
    const { project_id_or_name } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!project_id_or_name) {
        return baseApi.handleError(new Error('项目ID或名称不能为空'), 'PROJECT_ID_REQUIRED', '项目ID或名称不能为空')
      }

      // 查找项目
      const project = await this._findProjectByIdOrName(project_id_or_name)
      if (!project) {
        return baseApi.handleError(new Error('项目不存在'), 'PROJECT_NOT_FOUND', '项目不存在')
      }

      console.log('getProject 成功，项目ID：', project.id)
      return baseApi.formatResponse(project)
    } catch (error) {
      console.error('getProject 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'GET_PROJECT_FAILED', '获取项目详情失败')
    }
  },

  /**
   * 创建新项目
   * @param {object} params 参数对象
   * @param {string} params.name 项目名称
   * @param {string} [params.color] 项目颜色，如 "#FF0000" 表示红色
   * @returns {object} 创建的项目信息 (API 原始响应)
   */
  createProject: async function (params) {
    console.log('--- 调用 createProject ---', params)
    const { name, color } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!name) {
        return baseApi.handleError(new Error('项目名称不能为空'), 'PROJECT_NAME_REQUIRED', '项目名称不能为空')
      }

      // 构建项目数据
      const projectData = {
        name: name,
        inAll: true, // 滴答清单 API 通常需要此字段
      }

      if (color) {
        projectData.color = color
      }

      // 创建项目
      const response = await baseApi.makeRequest('/api/v2/project', 'POST', projectData)
      if (!response.success) {
        return response
      }

      console.log('createProject 成功，项目ID：', response.data.id)
      return response
    } catch (error) {
      console.error('createProject 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'CREATE_PROJECT_FAILED', '创建项目失败')
    }
  },

  /**
   * 更新项目信息
   * @param {object} params 参数对象
   * @param {string} params.project_id_or_name 项目ID或项目名称
   * @param {string} [params.name] 新项目名称
   * @param {string} [params.color] 新项目颜色
   * @returns {object} 更新操作的结果字典 (包含 success, info, data)
   */
  updateProject: async function (params) {
    console.log('--- 调用 updateProject ---', params)
    const { project_id_or_name, name, color } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!project_id_or_name) {
        return baseApi.handleError(new Error('项目ID或名称不能为空'), 'PROJECT_ID_REQUIRED', '项目ID或名称不能为空')
      }

      // 查找项目
      const project = await this._findProjectByIdOrName(project_id_or_name)
      if (!project) {
        return baseApi.handleError(new Error('项目不存在'), 'PROJECT_NOT_FOUND', '项目不存在')
      }

      // 构建更新数据
      const updateData = {
        id: project.id,
      }

      if (name !== undefined) updateData.name = name
      if (color !== undefined) updateData.color = color

      // 如果没有要更新的字段
      if (!name && !color) {
        return baseApi.formatResponse({
          success: true,
          info: '没有需要更新的字段',
          data: project,
        })
      }

      // 更新项目
      const response = await baseApi.makeRequest(`/api/v2/project/${project.id}`, 'POST', updateData)
      if (!response.success) {
        return response
      }

      console.log('updateProject 成功，项目ID：', project.id)
      return baseApi.formatResponse({
        success: true,
        info: '项目更新成功',
        data: response.data,
      })
    } catch (error) {
      console.error('updateProject 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'UPDATE_PROJECT_FAILED', '更新项目失败')
    }
  },

  /**
   * 删除项目
   * @param {object} params 参数对象
   * @param {string} params.project_id_or_name 项目ID或项目名称
   * @returns {object} 删除操作的响应字典 (包含 success, info, data)
   */
  deleteProject: async function (params) {
    console.log('--- 调用 deleteProject ---', params)
    const { project_id_or_name } = params || {}

    try {
      const baseApi = this._getBaseApi()

      // 验证必需参数
      if (!project_id_or_name) {
        return baseApi.handleError(new Error('项目ID或名称不能为空'), 'PROJECT_ID_REQUIRED', '项目ID或名称不能为空')
      }

      // 查找项目
      const project = await this._findProjectByIdOrName(project_id_or_name)
      if (!project) {
        return baseApi.handleError(new Error('项目不存在'), 'PROJECT_NOT_FOUND', '项目不存在')
      }

      // 删除项目
      const response = await baseApi.makeRequest(`/api/v2/project/${project.id}`, 'DELETE')
      if (!response.success) {
        return response
      }

      console.log('deleteProject 成功，项目ID：', project.id)
      return baseApi.formatResponse({
        success: true,
        info: '项目删除成功',
        data: response.data,
      })
    } catch (error) {
      console.error('deleteProject 失败：', error)
      const baseApi = this._getBaseApi()
      return baseApi.handleError(error, 'DELETE_PROJECT_FAILED', '删除项目失败')
    }
  },

  /**
   * 根据ID或名称查找项目
   * @param {string} idOrName 项目ID或名称
   * @returns {object|null} 找到的项目对象，未找到返回null
   */
  _findProjectByIdOrName: async function (idOrName) {
    try {
      const baseApi = this._getBaseApi()

      // 获取所有项目
      const response = await baseApi.makeRequest('/api/v2/batch/check/0', 'GET')
      if (!response.success) {
        return null
      }

      const projects = response.data.projectProfiles || []

      // 首先尝试按ID查找
      let project = projects.find((p) => p.id === idOrName)

      // 如果按ID未找到，尝试按名称查找
      if (!project) {
        project = projects.find((p) => p.name === idOrName)
      }

      // 如果仍未找到，尝试不区分大小写的名称匹配
      if (!project) {
        project = projects.find((p) => p.name && p.name.toLowerCase() === idOrName.toLowerCase())
      }

      return project || null
    } catch (error) {
      console.error('查找项目失败：', error)
      return null
    }
  },
}
