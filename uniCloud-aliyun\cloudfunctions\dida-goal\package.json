{"name": "dida-goal", "version": "1.0.0", "description": "滴答清单目标管理云函数 - 提供完整的目标 CRUD 操作和任务目标匹配功能", "main": "index.obj.js", "keywords": ["uniCloud", "云函数", "目标管理", "滴答清单", "dida365", "goal-management", "OKR", "任务匹配", "CRUD"], "author": "AI 开发团队", "license": "MIT", "engines": {"node": ">=14.0.0"}, "dependencies": {}, "devDependencies": {}, "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/your-org/okr-web.git"}, "bugs": {"url": "https://github.com/your-org/okr-web/issues"}, "homepage": "https://github.com/your-org/okr-web#readme", "cloudfunction-config": {"memorySize": 512, "timeout": 30, "runtime": "nodejs14"}}