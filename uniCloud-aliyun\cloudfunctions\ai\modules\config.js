/**
 * AI 配置和常量管理模块
 *
 * 主要功能：
 * 1. 管理豆包 AI 的连接配置参数
 * 2. 维护工具注册表，定义所有可用的云函数工具
 * 3. 定义 SSE 消息类型常量，统一前后端通信协议
 * 4. 提供默认系统提示词，指导 AI 进行意图识别
 *
 * 设计原则：
 * - 集中化配置管理，便于维护和修改
 * - 类型安全的参数定义，减少运行时错误
 * - 可扩展的工具注册机制，支持动态添加新工具
 * - 标准化的消息类型定义，确保通信协议一致性
 *
 * 使用场景：
 * - 初始化 AI 客户端时获取连接参数
 * - 执行计划生成时查询可用工具
 * - SSE 推送时使用标准消息类型
 * - 意图识别时使用默认系统提示词
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

/**
 * 豆包 AI 配置参数
 * 用于初始化 OpenAI 客户端，连接豆包 AI 服务
 *
 * 配置说明：
 * - baseURL: 豆包 AI 的 API 基础地址，使用北京区域的服务
 * - apiKey: API 访问密钥，生产环境应从环境变量或配置文件获取
 * - timeout: 请求超时时间，设置为 30 秒以平衡响应速度和稳定性
 *
 * 安全注意事项：
 * - API 密钥不应硬编码在代码中
 * - 建议使用环境变量或加密配置文件存储敏感信息
 * - 定期轮换 API 密钥以提高安全性
 */
const doubaoParams = {
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3', // 豆包 AI API 基础地址
  apiKey: 'a8b8b8b8-b8b8-b8b8-b8b8-b8b8b8b8b8b8', // API 密钥（生产环境需替换为真实密钥）
  timeout: 30000, // 请求超时时间（毫秒），30 秒超时
}

/**
 * 工具注册表
 * 定义所有可用的云函数工具及其配置信息
 *
 * 数据结构说明：
 * - cloudFunction: 对应的云函数名称，用于 uniCloud.importObject() 调用
 * - method: 云函数中的具体方法名
 * - description: 工具功能描述，用于 AI 理解工具用途
 * - parameters: 参数定义对象，包含类型、必需性、描述等信息
 * - metadata: 元数据信息，包含执行时间估算、工具分类等
 *
 * 工具分类：
 * - query: 查询类工具，用于获取数据
 * - action: 操作类工具，用于创建、修改、删除数据
 *
 * 扩展方式：
 * 添加新工具时，需要在此注册表中添加相应配置
 * 确保参数定义准确，以便参数验证器正确工作
 */
const TOOL_REGISTRY = {
  /**
   * 获取项目列表工具
   * 用于查询用户的所有项目或根据关键词筛选项目
   *
   * 使用场景：
   * - 用户询问"我有哪些项目"
   * - 需要根据项目名称查找特定项目
   * - 作为其他操作的前置步骤，获取项目上下文
   */
  getProjects: {
    cloudFunction: 'dida-todo', // 对应的云函数名称
    method: 'getProjects', // 调用的方法名
    description: '获取项目列表', // 工具功能描述
    parameters: {
      filter: {
        type: 'string', // 参数类型：字符串
        required: false, // 非必需参数
        description: '项目名称过滤关键词', // 参数说明
      },
    },
    metadata: {
      estimatedTime: 1500, // 预估执行时间（毫秒）
      category: 'query', // 工具分类：查询类
    },
  },
  /**
   * 获取任务列表工具
   * 用于查询指定项目下的任务或全局任务列表
   *
   * 使用场景：
   * - 用户询问"我有哪些任务"
   * - 查看特定项目下的任务
   * - 筛选已完成或未完成的任务
   */
  getTasks: {
    cloudFunction: 'dida-todo',
    method: 'getTasks',
    description: '获取任务列表',
    parameters: {
      mode: {
        type: 'string',
        required: false,
        description: '任务模式，支持 all(所有), today(今天), yesterday(昨天), recent_7_days(最近7天)',
      },
      keyword: { type: 'string', required: false, description: '关键词筛选' },
      priority: { type: 'number', required: false, description: '优先级筛选 (0-最低, 1-低, 3-中, 5-高)' },
      project_name: { type: 'string', required: false, description: '项目名称筛选' },
      completed: {
        type: 'boolean',
        required: false,
        description: '是否已完成，true表示已完成，false表示未完成，null表示全部',
      },
    },
    metadata: {
      estimatedTime: 2000, // 任务查询可能涉及更多数据，预估时间稍长
      category: 'query',
    },
  },

  /**
   * 创建任务工具
   * 用于根据用户输入创建新的任务
   *
   * 使用场景：
   * - 用户说"帮我创建一个任务"
   * - 从用户描述中提取任务信息并创建
   * - 批量创建多个相关任务
   */
  createTask: {
    cloudFunction: 'dida-todo',
    method: 'createTask',
    description: '创建新任务',
    parameters: {
      title: { type: 'string', required: false, description: '任务标题' },
      content: { type: 'string', required: false, description: '任务内容' },
      priority: { type: 'number', required: false, description: '优先级 (0-最低, 1-低, 3-中, 5-高)' },
      project_name: { type: 'string', required: false, description: '项目名称' },
      tag_names: { type: 'array', required: false, description: '标签名称列表' },
      start_date: { type: 'string', required: false, description: '开始日期，格式 YYYY-MM-DD HH:MM:SS' },
      due_date: { type: 'string', required: false, description: '截止日期，格式 YYYY-MM-DD HH:MM:SS' },
      is_all_day: { type: 'boolean', required: false, description: '是否为全天任务' },
      reminder: {
        type: 'string',
        required: false,
        description: '提醒选项，如 "0"(准时), "-5M"(提前5分钟), "-1H"(提前1小时), "-1D"(提前1天)',
      },
    },
    metadata: {
      estimatedTime: 2500, // 创建操作涉及数据写入，预估时间较长
      category: 'action', // 操作类工具
    },
  },

  /**
   * 更新任务工具
   * 用于修改现有任务的信息
   *
   * 使用场景：
   * - 用户说"修改某个任务"
   * - 更新任务状态、内容、截止时间等
   * - 标记任务为已完成或未完成
   */
  updateTask: {
    cloudFunction: 'dida-todo',
    method: 'updateTask',
    description: '更新任务',
    parameters: {
      task_id_or_title: { type: 'string', required: true, description: '任务ID或任务标题' },
      title: { type: 'string', required: false, description: '新任务标题' },
      content: { type: 'string', required: false, description: '新任务内容' },
      priority: { type: 'number', required: false, description: '新优先级 (0-最低, 1-低, 3-中, 5-高)' },
      project_name: { type: 'string', required: false, description: '新项目名称' },
      tag_names: { type: 'array', required: false, description: '新标签名称列表' },
      start_date: { type: 'string', required: false, description: '新开始日期，格式 YYYY-MM-DD HH:MM:SS' },
      due_date: { type: 'string', required: false, description: '新截止日期，格式 YYYY-MM-DD HH:MM:SS' },
      is_all_day: { type: 'boolean', required: false, description: '是否为全天任务' },
      reminder: { type: 'string', required: false, description: '新提醒选项' },
      status: { type: 'number', required: false, description: '新状态，0表示未完成，2表示已完成' },
    },
    metadata: {
      estimatedTime: 2500,
      category: 'action',
    },
  },

  /**
   * 删除任务工具
   * 用于删除指定的任务
   *
   * 使用场景：
   * - 用户说"删除某个任务"
   * - 清理不需要的任务
   * - 批量删除任务
   */
  deleteTask: {
    cloudFunction: 'dida-todo',
    method: 'deleteTask',
    description: '删除任务',
    parameters: {
      task_id_or_title: { type: 'string', required: true, description: '任务ID或任务标题' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action',
    },
  },

  /**
   * 获取项目详情工具
   * 用于获取特定项目的详细信息
   *
   * 使用场景：
   * - 需要了解项目的具体信息
   * - 作为其他操作的前置步骤，获取项目详情
   * - 验证项目是否存在
   */
  getProject: {
    cloudFunction: 'dida-todo',
    method: 'getProject',
    description: '获取项目详情',
    parameters: {
      project_id_or_name: { type: 'string', required: true, description: '项目ID或项目名称' },
    },
    metadata: {
      estimatedTime: 1500,
      category: 'query',
    },
  },

  /**
   * 创建项目工具
   * 用于创建新的项目
   *
   * 使用场景：
   * - 用户说"创建一个新项目"
   * - 组织任务时需要新的项目分类
   * - 建立新的工作领域
   */
  createProject: {
    cloudFunction: 'dida-todo',
    method: 'createProject',
    description: '创建新项目',
    parameters: {
      name: { type: 'string', required: true, description: '项目名称' },
      color: { type: 'string', required: false, description: '项目颜色，如 "#FF0000" 表示红色' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action',
    },
  },

  /**
   * 更新项目工具
   * 用于修改现有项目的信息
   *
   * 使用场景：
   * - 用户说"修改项目名称"
   * - 更新项目颜色或其他属性
   * - 重新组织项目结构
   */
  updateProject: {
    cloudFunction: 'dida-todo',
    method: 'updateProject',
    description: '更新项目信息',
    parameters: {
      project_id_or_name: { type: 'string', required: true, description: '项目ID或项目名称' },
      name: { type: 'string', required: false, description: '新项目名称' },
      color: { type: 'string', required: false, description: '新项目颜色' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action',
    },
  },

  /**
   * 删除项目工具
   * 用于删除指定的项目
   *
   * 使用场景：
   * - 用户说"删除某个项目"
   * - 清理不需要的项目
   * - 重新整理项目结构
   */
  deleteProject: {
    cloudFunction: 'dida-todo',
    method: 'deleteProject',
    description: '删除项目',
    parameters: {
      project_id_or_name: { type: 'string', required: true, description: '项目ID或项目名称' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action',
    },
  },

  // ==================== 标签管理工具 ====================

  /**
   * 获取标签列表工具
   * 用于查询用户的所有标签
   *
   * 使用场景：
   * - 用户询问"我有哪些标签"
   * - 查看所有可用的标签
   * - 作为其他操作的前置步骤，获取标签上下文
   */
  getTags: {
    cloudFunction: 'dida-tag',
    method: 'getTags',
    description: '获取标签列表',
    parameters: {},
    metadata: {
      estimatedTime: 1500,
      category: 'query',
    },
  },

  /**
   * 创建标签工具
   * 用于创建新的标签
   *
   * 使用场景：
   * - 用户说"创建一个新标签"
   * - 为任务分类创建新的标签
   * - 建立新的任务分类体系
   */
  createTag: {
    cloudFunction: 'dida-tag',
    method: 'createTag',
    description: '创建新标签',
    parameters: {
      name: { type: 'string', required: true, description: '标签名称' },
      color: { type: 'string', required: false, description: '标签颜色，如 "#FF0000" 表示红色' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action',
    },
  },

  /**
   * 更新标签工具
   * 用于修改现有标签的信息
   *
   * 使用场景：
   * - 用户说"修改标签名称"
   * - 更新标签颜色或其他属性
   * - 重新组织标签体系
   */
  updateTag: {
    cloudFunction: 'dida-tag',
    method: 'updateTag',
    description: '更新标签信息',
    parameters: {
      tag_id_or_name: { type: 'string', required: true, description: '标签ID或标签名称' },
      name: { type: 'string', required: false, description: '新标签名称' },
      color: { type: 'string', required: false, description: '新标签颜色' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action',
    },
  },

  /**
   * 删除标签工具
   * 用于删除指定的标签
   *
   * 使用场景：
   * - 用户说"删除某个标签"
   * - 清理不需要的标签
   * - 重新整理标签体系
   */
  deleteTag: {
    cloudFunction: 'dida-tag',
    method: 'deleteTag',
    description: '删除标签',
    parameters: {
      tag_id_or_name: { type: 'string', required: true, description: '标签ID或标签名称' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action',
    },
  },

  /**
   * 重命名标签工具
   * 用于将标签重命名为新名称
   *
   * 使用场景：
   * - 用户说"将标签重命名"
   * - 标准化标签命名
   * - 修正标签名称错误
   */
  renameTag: {
    cloudFunction: 'dida-tag',
    method: 'renameTag',
    description: '重命名标签',
    parameters: {
      old_name: { type: 'string', required: true, description: '旧标签名称' },
      new_name: { type: 'string', required: true, description: '新标签名称' },
    },
    metadata: {
      estimatedTime: 2500,
      category: 'action',
    },
  },

  /**
   * 合并标签工具
   * 用于将两个标签合并为一个
   *
   * 使用场景：
   * - 用户说"合并重复的标签"
   * - 清理冗余的标签
   * - 标准化标签体系
   */
  mergeTags: {
    cloudFunction: 'dida-tag',
    method: 'mergeTags',
    description: '合并标签',
    parameters: {
      source_name: { type: 'string', required: true, description: '源标签名称（将被合并的标签）' },
      target_name: { type: 'string', required: true, description: '目标标签名称（合并到的标签）' },
    },
    metadata: {
      estimatedTime: 3000,
      category: 'action',
    },
  },

  // ==================== 目标管理工具 ====================

  /**
   * 创建目标工具
   * 用于创建新的目标
   *
   * 使用场景：
   * - 用户说"创建一个新目标"
   * - 设定阶段性目标、永久目标或习惯目标
   * - 建立目标管理体系
   */
  createGoal: {
    cloudFunction: 'dida-goal',
    method: 'createGoal',
    description: '创建新目标',
    parameters: {
      title: { type: 'string', required: true, description: '目标标题' },
      type: { type: 'string', required: true, description: '目标类型 (phase/permanent/habit)' },
      keywords: { type: 'string', required: true, description: '关键词，以逗号分隔' },
      description: { type: 'string', required: false, description: '目标的基础描述' },
      due_date: { type: 'string', required: false, description: '截止日期 (YYYY-MM-DD)，阶段性目标必填' },
      start_date: { type: 'string', required: false, description: '开始日期 (YYYY-MM-DD)' },
      frequency: { type: 'string', required: false, description: '频率 (daily, weekly:1,3,5 等)，习惯目标必填' },
      related_projects: { type: 'array', required: false, description: '相关项目IDs' },
    },
    metadata: {
      estimatedTime: 3000,
      category: 'action',
    },
  },

  /**
   * 获取目标列表工具
   * 用于查询用户的所有目标
   *
   * 使用场景：
   * - 用户询问"我有哪些目标"
   * - 查看所有可用的目标
   * - 按类型或状态筛选目标
   */
  getGoals: {
    cloudFunction: 'dida-goal',
    method: 'getGoals',
    description: '获取目标列表',
    parameters: {
      type: { type: 'string', required: false, description: '目标类型筛选 (phase/permanent/habit)' },
      status: { type: 'string', required: false, description: '目标状态筛选 (active/completed)' },
      keywords: { type: 'string', required: false, description: '关键词筛选（匹配目标标题或关键词）' },
    },
    metadata: {
      estimatedTime: 1500,
      category: 'query',
    },
  },

  /**
   * 获取目标详情工具
   * 用于查询指定目标的详细信息
   *
   * 使用场景：
   * - 用户询问"某个目标的详情"
   * - 查看目标的完整信息
   * - 了解目标的进度和状态
   */
  getGoal: {
    cloudFunction: 'dida-goal',
    method: 'getGoal',
    description: '获取目标详情',
    parameters: {
      goal_id: { type: 'string', required: true, description: '目标ID' },
    },
    metadata: {
      estimatedTime: 1500,
      category: 'query',
    },
  },

  /**
   * 更新目标工具
   * 用于修改现有目标的信息
   *
   * 使用场景：
   * - 用户说"修改目标信息"
   * - 更新目标状态或进度
   * - 调整目标时间或描述
   */
  updateGoal: {
    cloudFunction: 'dida-goal',
    method: 'updateGoal',
    description: '更新目标',
    parameters: {
      goal_id: { type: 'string', required: true, description: '目标ID' },
      title: { type: 'string', required: false, description: '新标题' },
      type: { type: 'string', required: false, description: '新类型 (phase/permanent/habit)' },
      status: { type: 'string', required: false, description: '新状态 (active/completed)' },
      keywords: { type: 'string', required: false, description: '新关键词（逗号分隔）' },
      description: { type: 'string', required: false, description: '新的基础描述' },
      due_date: { type: 'string', required: false, description: '新截止日期 (YYYY-MM-DD)' },
      start_date: { type: 'string', required: false, description: '新开始日期 (YYYY-MM-DD)' },
      frequency: { type: 'string', required: false, description: '新频率' },
    },
    metadata: {
      estimatedTime: 2500,
      category: 'action',
    },
  },

  /**
   * 删除目标工具
   * 用于删除指定的目标
   *
   * 使用场景：
   * - 用户说"删除某个目标"
   * - 清理不需要的目标
   * - 重新整理目标体系
   */
  deleteGoal: {
    cloudFunction: 'dida-goal',
    method: 'deleteGoal',
    description: '删除目标',
    parameters: {
      goal_id: { type: 'string', required: true, description: '目标ID' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'action',
    },
  },

  /**
   * 任务目标匹配工具
   * 用于智能匹配任务与相关目标
   *
   * 使用场景：
   * - 用户询问"这个任务属于哪个目标"
   * - 自动关联任务与目标
   * - 分析任务的目标归属
   */
  matchTaskWithGoals: {
    cloudFunction: 'dida-goal',
    method: 'matchTaskWithGoals',
    description: '匹配任务与目标',
    parameters: {
      task_title: { type: 'string', required: true, description: '任务标题' },
      task_content: { type: 'string', required: false, description: '任务内容' },
      project_id: { type: 'string', required: false, description: '任务所属项目ID' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'analysis',
    },
  },

  // ==================== 数据分析工具 ====================

  /**
   * 目标统计分析工具
   * 用于获取目标的综合统计信息和分析数据
   *
   * 使用场景：
   * - 用户询问"我的目标完成情况如何"
   * - 需要查看目标统计数据
   * - 分析目标进展趋势
   */
  getGoalStatistics: {
    cloudFunction: 'dida-analytics',
    method: 'getGoalStatistics',
    description: '获取目标统计信息',
    parameters: {
      force_refresh: { type: 'boolean', required: false, description: '是否强制刷新缓存数据' },
    },
    metadata: {
      estimatedTime: 3000,
      category: 'analytics',
    },
  },

  /**
   * 目标进度历史工具
   * 用于获取指定目标的进度变化历史和趋势分析
   *
   * 使用场景：
   * - 用户询问"某个目标的进度如何"
   * - 查看目标进度变化趋势
   * - 分析目标执行情况
   */
  getGoalProgress: {
    cloudFunction: 'dida-analytics',
    method: 'getGoalProgress',
    description: '获取目标进度历史',
    parameters: {
      goal_id: { type: 'string', required: true, description: '目标ID' },
    },
    metadata: {
      estimatedTime: 2500,
      category: 'analytics',
    },
  },

  /**
   * 任务统计分析工具
   * 用于获取任务的统计信息和效率分析
   *
   * 使用场景：
   * - 用户询问"我的任务完成情况"
   * - 查看任务统计数据
   * - 分析工作效率
   */
  getTaskStatistics: {
    cloudFunction: 'dida-analytics',
    method: 'getTaskStatistics',
    description: '获取任务统计信息',
    parameters: {
      days: { type: 'integer', required: false, description: '统计的天数范围（默认30天）' },
      force_refresh: { type: 'boolean', required: false, description: '是否强制刷新缓存数据' },
    },
    metadata: {
      estimatedTime: 3000,
      category: 'analytics',
    },
  },

  /**
   * 任务关键词提取工具
   * 用于从任务中提取关键词和主题分析
   *
   * 使用场景：
   * - 用户询问"我主要在做什么类型的任务"
   * - 分析任务主题分布
   * - 提取工作重点
   */
  extractTaskKeywords: {
    cloudFunction: 'dida-analytics',
    method: 'extractTaskKeywords',
    description: '从任务中提取关键词',
    parameters: {
      limit: { type: 'integer', required: false, description: '返回的关键词数量（默认20）' },
      force_refresh: { type: 'boolean', required: false, description: '是否强制刷新缓存数据' },
    },
    metadata: {
      estimatedTime: 4000,
      category: 'analytics',
    },
  },

  /**
   * 目标完成预测工具
   * 用于预测目标的完成情况和风险评估
   *
   * 使用场景：
   * - 用户询问"这个目标能按时完成吗"
   * - 预测目标完成时间
   * - 评估完成风险
   */
  predictGoalCompletion: {
    cloudFunction: 'dida-analytics',
    method: 'predictGoalCompletion',
    description: '预测目标完成情况',
    parameters: {
      goal_id: { type: 'string', required: true, description: '目标ID' },
      force_refresh: { type: 'boolean', required: false, description: '是否强制刷新缓存数据' },
    },
    metadata: {
      estimatedTime: 5000,
      category: 'analytics',
    },
  },

  /**
   * 目标报告生成工具
   * 用于生成指定目标的详细分析报告
   *
   * 使用场景：
   * - 用户询问"生成目标分析报告"
   * - 需要详细的目标分析
   * - 制作目标总结
   */
  generateGoalReport: {
    cloudFunction: 'dida-analytics',
    method: 'generateGoalReport',
    description: '生成目标报告',
    parameters: {
      goal_id: { type: 'string', required: true, description: '目标ID' },
      force_refresh: { type: 'boolean', required: false, description: '是否强制刷新缓存数据' },
    },
    metadata: {
      estimatedTime: 6000,
      category: 'analytics',
    },
  },

  /**
   * 每周总结生成工具
   * 用于生成过去一周的综合数据分析报告
   *
   * 使用场景：
   * - 用户询问"生成本周总结"
   * - 需要周度工作回顾
   * - 制作周报
   */
  generateWeeklySummary: {
    cloudFunction: 'dida-analytics',
    method: 'generateWeeklySummary',
    description: '生成每周总结',
    parameters: {
      force_refresh: { type: 'boolean', required: false, description: '是否强制刷新缓存数据' },
    },
    metadata: {
      estimatedTime: 8000,
      category: 'analytics',
    },
  },
}

/**
 * SSE 消息类型常量
 * 定义所有 SSE 推送消息的类型标识，确保前后端通信协议的一致性
 *
 * 消息类型分类：
 * 1. 基础流程消息：处理用户请求的基本流程
 * 2. 意图识别消息：AI 意图解析相关的消息
 * 3. 任务执行消息：智能任务执行过程中的消息
 *
 * 使用方式：
 * - 后端推送消息时使用这些类型标识
 * - 前端根据消息类型进行不同的处理逻辑
 * - 便于消息类型的统一管理和维护
 *
 * 扩展原则：
 * - 新增消息类型时应遵循命名规范
 * - 保持向后兼容性，避免修改已有类型
 * - 添加详细的中文描述，便于理解
 */
const SSE_MESSAGE_TYPES = {
  // 基础流程消息类型
  start: '开始生成回复', // 开始处理用户请求，通知前端显示加载状态
  intent_type: '意图类型识别', // 识别到用户意图类型，让前端知道请求的性质
  intent_content_start: '意图内容开始', // 开始推送意图内容，准备接收具体内容
  intent_content_chunk: '意图内容块', // 意图内容数据块，流式推送的内容片段
  end: '结束', // 处理完成，通知前端停止等待
  error: '错误', // 处理出错，通知前端显示错误信息

  // 任务执行相关消息类型（V1.1 新增）
  execution_plan_start: '执行计划开始', // 开始执行任务计划，显示计划信息
  execution_step: '执行步骤', // 当前执行步骤信息，显示进度
  step_result: '步骤结果', // 步骤执行结果，显示每步的执行情况
  step_error: '步骤错误', // 步骤执行出错，显示具体错误信息
  execution_complete: '执行完成', // 任务执行完成，显示最终结果
  execution_failed: '执行失败', // 任务执行失败，显示失败原因
}

/**
 * 默认系统提示词
 * 用于指导 AI 进行意图识别和内容提取的核心提示词
 *
 * 设计目标：
 * 1. 准确识别用户的真实意图
 * 2. 标准化 AI 的输出格式
 * 3. 确保后续处理的可靠性
 *
 * 意图类型定义（简化为两种）：
 * - task: 任务操作意图，包括创建、查询、修改、删除等所有任务相关操作
 * - chat: 普通聊天意图，不涉及具体的任务操作
 *
 * 输出格式要求：
 * - 使用中文引号「」标识关键字段
 * - task 类型只输出意图类型，不输出闲聊回复
 * - chat 类型需要输出闲聊回复内容
 * - 不允许添加额外的解释或内容
 *
 * 关键词匹配规则：
 * - 任务类：创建、添加、设置、安排、新建、制定、查询、搜索、查看、显示、列出、找到、修改、更新、删除、完成、统计等
 * - 聊天类：问候、询问、讨论、解释等非任务操作
 */
const DEFAULT_SYSTEM_PROMPT = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

请分析用户输入内容，并将其归类为以下两种意图之一：
1. task: 当用户想要执行任何与任务相关的操作时（包括创建、查询、修改、删除任务等）
2. chat: 其他所有不属于任务操作的内容，视为一般闲聊对话

分析完成后，必须严格按照以下格式输出结果：
- 如果是 task 类型，只输出：「意图类型」：task
- 如果是 chat 类型，输出：
  「意图类型」：chat
  「闲聊回复」：[针对用户问题的回复内容]

注意：
- 分析要准确，不要混淆不同意图类型
- task 类型只输出一行意图类型，不需要额外内容
- chat 类型需要输出两行：意图类型和闲聊回复
- 确保格式严格遵循示例，包括使用中文引号「」
- task 类型包括：创建任务、查询任务、修改任务、删除任务、任务统计等所有任务相关操作
- chat 类型包括：问候、闲聊、询问非任务相关问题、讨论、解释等`

module.exports = {
  doubaoParams,
  TOOL_REGISTRY,
  SSE_MESSAGE_TYPES,
  DEFAULT_SYSTEM_PROMPT,
}
