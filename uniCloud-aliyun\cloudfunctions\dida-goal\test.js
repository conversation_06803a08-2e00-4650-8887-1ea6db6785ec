/**
 * dida-goal 云函数测试
 * 
 * 测试目标：
 * 1. 验证目标 CRUD 操作的完整性
 * 2. 验证任务目标匹配功能
 * 3. 确保与 MCP 实现的 API 兼容性
 * 4. 验证 dida-base 云函数的集成
 * 5. 验证目标数据存储机制
 * 
 * 测试覆盖：
 * - 目标：createGoal, getGoals, getGoal, updateGoal, deleteGoal
 * - 匹配：matchTaskWithGoals
 * - 存储：目标管理项目创建和元数据管理
 * - 错误处理和边界条件
 * - API 响应格式兼容性
 * 
 * <AUTHOR> 开发团队
 * @version 1.0.0
 * @since 2024-01-01
 */

// 模拟 uniCloud 环境
const mockUniCloud = {
  database: () => ({
    collection: (name) => ({
      where: () => ({
        orderBy: () => ({
          limit: () => ({
            get: async () => ({ data: [] })
          })
        })
      }),
      add: async (data) => ({ id: 'mock_id_' + Date.now() })
    })
  }),
  importObject: (name) => {
    if (name === 'dida-base') {
      return {
        makeRequest: async (url, method, data) => {
          // 模拟 API 响应
          if (url === '/api/v2/batch/check/0') {
            return {
              success: true,
              data: {
                projectProfiles: [
                  { id: 'goal_project_1', name: '目标管理', color: '#9C27B0' },
                  { id: 'project_1', name: '工作项目', color: '#FF0000' },
                  { id: 'project_2', name: '学习项目', color: '#00FF00' }
                ],
                syncTaskBean: {
                  update: [
                    {
                      id: 'goal_1',
                      title: '学习编程',
                      content: JSON.stringify({
                        type: 'phase',
                        keywords: ['编程', '学习', '技能'],
                        description: '提升编程技能',
                        status: 'active',
                        progress: 30,
                        dueDate: '2024-12-31',
                        createdTime: '2024-01-01T00:00:00.000Z'
                      }),
                      projectId: 'goal_project_1',
                      status: 0,
                      dueDate: '2024-12-31T23:59:59.000Z'
                    },
                    {
                      id: 'goal_2',
                      title: '健康生活',
                      content: JSON.stringify({
                        type: 'habit',
                        keywords: ['健康', '运动', '生活'],
                        description: '保持健康的生活方式',
                        status: 'active',
                        progress: 60,
                        frequency: 'daily',
                        createdTime: '2024-01-01T00:00:00.000Z'
                      }),
                      projectId: 'goal_project_1',
                      status: 0
                    }
                  ]
                }
              }
            }
          }
          
          if (method === 'POST' && url === '/api/v2/project') {
            return {
              success: true,
              data: { id: 'goal_project_' + Date.now(), ...data }
            }
          }
          
          if (method === 'POST' && url.includes('/task')) {
            return {
              success: true,
              data: { id: 'goal_' + Date.now(), ...data }
            }
          }
          
          if (method === 'POST' && url.includes('/task/')) {
            return {
              success: true,
              data: { id: url.split('/').pop(), ...data }
            }
          }
          
          if (method === 'DELETE' && url.includes('/task/')) {
            return {
              success: true,
              data: { message: '目标删除成功' }
            }
          }
          
          return { success: true, data: {} }
        },
        handleError: (error, code, message) => ({
          success: false,
          error: { code, message, details: error.message }
        }),
        formatResponse: (data) => ({
          success: true,
          data
        })
      }
    }
    return {}
  }
}

// 设置全局 uniCloud
global.uniCloud = mockUniCloud

// 导入要测试的云函数
const didaGoal = require('./index.obj.js')

/**
 * 测试工具函数
 */
class TestRunner {
  constructor() {
    this.tests = []
    this.passed = 0
    this.failed = 0
  }

  /**
   * 添加测试用例
   */
  test(name, testFn) {
    this.tests.push({ name, testFn })
  }

  /**
   * 断言函数
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message || '断言失败')
    }
  }

  /**
   * 运行所有测试
   */
  async run() {
    console.log('🚀 开始运行 dida-goal 目标管理测试...\n')

    for (const { name, testFn } of this.tests) {
      try {
        console.log(`📋 运行测试: ${name}`)
        await testFn.call(this)
        this.passed++
        console.log(`✅ 测试通过: ${name}\n`)
      } catch (error) {
        this.failed++
        console.error(`❌ 测试失败: ${name}`)
        console.error(`   错误: ${error.message}\n`)
      }
    }

    console.log('📊 测试结果汇总:')
    console.log(`   ✅ 通过: ${this.passed}`)
    console.log(`   ❌ 失败: ${this.failed}`)
    console.log(`   📈 成功率: ${((this.passed / (this.passed + this.failed)) * 100).toFixed(1)}%`)

    return this.failed === 0
  }
}

// 创建测试运行器
const runner = new TestRunner()

// ==================== 目标管理项目测试 ====================

runner.test('_getGoalProject - 获取目标管理项目', async function() {
  const goalProject = await didaGoal._getGoalProject()
  
  this.assert(goalProject.id, '应该返回项目ID')
  this.assert(goalProject.name === '目标管理', '项目名称应该是"目标管理"')
})

// ==================== 目标 CRUD 测试 ====================

runner.test('createGoal - 创建阶段性目标', async function() {
  const result = await didaGoal.createGoal({
    title: '学习 Vue.js',
    type: 'phase',
    keywords: 'Vue,前端,学习',
    description: '深入学习 Vue.js 框架',
    due_date: '2024-06-30'
  })
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(result.data.id, '应该返回目标ID')
})

runner.test('createGoal - 创建习惯目标', async function() {
  const result = await didaGoal.createGoal({
    title: '每日阅读',
    type: 'habit',
    keywords: '阅读,学习,习惯',
    description: '每天阅读30分钟',
    frequency: 'daily'
  })
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(result.data.id, '应该返回目标ID')
})

runner.test('createGoal - 创建永久目标', async function() {
  const result = await didaGoal.createGoal({
    title: '成为技术专家',
    type: 'permanent',
    keywords: '技术,专家,成长',
    description: '在技术领域成为专家'
  })
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(result.data.id, '应该返回目标ID')
})

runner.test('createGoal - 参数验证（缺少标题）', async function() {
  const result = await didaGoal.createGoal({
    type: 'phase',
    keywords: '测试'
  })
  
  this.assert(result.success === false, '缺少标题应该返回失败')
  this.assert(result.error.code === 'GOAL_TITLE_REQUIRED', '应该返回正确的错误代码')
})

runner.test('createGoal - 参数验证（无效类型）', async function() {
  const result = await didaGoal.createGoal({
    title: '测试目标',
    type: 'invalid',
    keywords: '测试'
  })
  
  this.assert(result.success === false, '无效类型应该返回失败')
  this.assert(result.error.code === 'INVALID_GOAL_TYPE', '应该返回正确的错误代码')
})

runner.test('createGoal - 参数验证（阶段性目标缺少截止日期）', async function() {
  const result = await didaGoal.createGoal({
    title: '测试目标',
    type: 'phase',
    keywords: '测试'
  })
  
  this.assert(result.success === false, '阶段性目标缺少截止日期应该返回失败')
  this.assert(result.error.code === 'PHASE_GOAL_DUE_DATE_REQUIRED', '应该返回正确的错误代码')
})

runner.test('createGoal - 参数验证（习惯目标缺少频率）', async function() {
  const result = await didaGoal.createGoal({
    title: '测试目标',
    type: 'habit',
    keywords: '测试'
  })
  
  this.assert(result.success === false, '习惯目标缺少频率应该返回失败')
  this.assert(result.error.code === 'HABIT_GOAL_FREQUENCY_REQUIRED', '应该返回正确的错误代码')
})

runner.test('getGoals - 获取所有目标', async function() {
  const result = await didaGoal.getGoals()
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(Array.isArray(result.data), '应该返回目标数组')
  this.assert(result.data.length >= 0, '目标数组应该有效')
  
  // 验证目标数据结构
  if (result.data.length > 0) {
    const goal = result.data[0]
    this.assert(goal.id, '目标应该有ID')
    this.assert(goal.title, '目标应该有标题')
    this.assert(goal.type, '目标应该有类型')
    this.assert(Array.isArray(goal.keywords), '关键词应该是数组')
  }
})

runner.test('getGoals - 按类型筛选', async function() {
  const result = await didaGoal.getGoals({ type: 'phase' })
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(Array.isArray(result.data), '应该返回目标数组')
})

runner.test('getGoals - 按状态筛选', async function() {
  const result = await didaGoal.getGoals({ status: 'active' })
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(Array.isArray(result.data), '应该返回目标数组')
})

runner.test('getGoals - 按关键词筛选', async function() {
  const result = await didaGoal.getGoals({ keywords: '学习' })
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(Array.isArray(result.data), '应该返回目标数组')
})

runner.test('getGoal - 获取目标详情', async function() {
  const result = await didaGoal.getGoal({ goal_id: 'goal_1' })
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(result.data.id === 'goal_1', '应该返回正确的目标')
})

runner.test('getGoal - 参数验证', async function() {
  const result = await didaGoal.getGoal({})
  
  this.assert(result.success === false, '缺少目标ID应该返回失败')
  this.assert(result.error.code === 'GOAL_ID_REQUIRED', '应该返回正确的错误代码')
})

runner.test('getGoal - 目标不存在', async function() {
  const result = await didaGoal.getGoal({ goal_id: 'nonexistent' })

  this.assert(result.success === false, '不存在的目标应该返回失败')
  this.assert(result.error.code === 'GOAL_NOT_FOUND', '应该返回正确的错误代码')
})

runner.test('updateGoal - 更新目标标题', async function() {
  const result = await didaGoal.updateGoal({
    goal_id: 'goal_1',
    title: '深入学习编程'
  })

  this.assert(result.success === true, '应该返回成功状态')
})

runner.test('updateGoal - 更新目标状态', async function() {
  const result = await didaGoal.updateGoal({
    goal_id: 'goal_1',
    status: 'completed'
  })

  this.assert(result.success === true, '应该返回成功状态')
})

runner.test('updateGoal - 更新目标类型', async function() {
  const result = await didaGoal.updateGoal({
    goal_id: 'goal_1',
    type: 'permanent'
  })

  this.assert(result.success === true, '应该返回成功状态')
})

runner.test('updateGoal - 参数验证', async function() {
  const result = await didaGoal.updateGoal({
    title: '新标题'
  })

  this.assert(result.success === false, '缺少目标ID应该返回失败')
  this.assert(result.error.code === 'GOAL_ID_REQUIRED', '应该返回正确的错误代码')
})

runner.test('updateGoal - 目标不存在', async function() {
  const result = await didaGoal.updateGoal({
    goal_id: 'nonexistent',
    title: '新标题'
  })

  this.assert(result.success === false, '不存在的目标应该返回失败')
  this.assert(result.error.code === 'GOAL_NOT_FOUND', '应该返回正确的错误代码')
})

runner.test('updateGoal - 无效目标类型', async function() {
  const result = await didaGoal.updateGoal({
    goal_id: 'goal_1',
    type: 'invalid'
  })

  this.assert(result.success === false, '无效类型应该返回失败')
  this.assert(result.error.code === 'INVALID_GOAL_TYPE', '应该返回正确的错误代码')
})

runner.test('deleteGoal - 删除目标', async function() {
  const result = await didaGoal.deleteGoal({ goal_id: 'goal_2' })

  this.assert(result.success === true, '应该返回成功状态')
})

runner.test('deleteGoal - 参数验证', async function() {
  const result = await didaGoal.deleteGoal({})

  this.assert(result.success === false, '缺少目标ID应该返回失败')
  this.assert(result.error.code === 'GOAL_ID_REQUIRED', '应该返回正确的错误代码')
})

runner.test('deleteGoal - 目标不存在', async function() {
  const result = await didaGoal.deleteGoal({ goal_id: 'nonexistent' })

  this.assert(result.success === false, '不存在的目标应该返回失败')
  this.assert(result.error.code === 'GOAL_NOT_FOUND', '应该返回正确的错误代码')
})

// ==================== 任务目标匹配测试 ====================

runner.test('matchTaskWithGoals - 关键词匹配', async function() {
  const result = await didaGoal.matchTaskWithGoals({
    task_title: '学习 JavaScript 编程',
    task_content: '深入学习 JavaScript 语言特性'
  })

  this.assert(result.success === true, '应该返回成功状态')
  this.assert(Array.isArray(result.data), '应该返回匹配数组')

  // 验证匹配结果结构
  if (result.data.length > 0) {
    const match = result.data[0]
    this.assert(match.goal, '匹配结果应该包含目标对象')
    this.assert(typeof match.score === 'number', '匹配结果应该包含得分')
    this.assert(Array.isArray(match.matchedKeywords), '匹配结果应该包含匹配的关键词')
    this.assert(match.matchReason, '匹配结果应该包含匹配原因')
  }
})

runner.test('matchTaskWithGoals - 标题匹配', async function() {
  const result = await didaGoal.matchTaskWithGoals({
    task_title: '健康饮食计划',
    task_content: '制定健康的饮食计划'
  })

  this.assert(result.success === true, '应该返回成功状态')
  this.assert(Array.isArray(result.data), '应该返回匹配数组')
})

runner.test('matchTaskWithGoals - 项目关联匹配', async function() {
  const result = await didaGoal.matchTaskWithGoals({
    task_title: '完成项目任务',
    task_content: '完成当前项目的开发任务',
    project_id: 'project_1'
  })

  this.assert(result.success === true, '应该返回成功状态')
  this.assert(Array.isArray(result.data), '应该返回匹配数组')
})

runner.test('matchTaskWithGoals - 无匹配结果', async function() {
  const result = await didaGoal.matchTaskWithGoals({
    task_title: '完全不相关的任务',
    task_content: '这是一个完全不相关的任务'
  })

  this.assert(result.success === true, '应该返回成功状态')
  this.assert(Array.isArray(result.data), '应该返回匹配数组')
  this.assert(result.data.length === 0, '应该返回空匹配数组')
})

runner.test('matchTaskWithGoals - 参数验证', async function() {
  const result = await didaGoal.matchTaskWithGoals({
    task_content: '任务内容'
  })

  this.assert(result.success === false, '缺少任务标题应该返回失败')
  this.assert(result.error.code === 'TASK_TITLE_REQUIRED', '应该返回正确的错误代码')
})

// ==================== 辅助方法测试 ====================

runner.test('_findGoalById - 查找存在的目标', async function() {
  const goal = await didaGoal._findGoalById('goal_1')

  this.assert(goal !== null, '应该找到目标')
  this.assert(goal.id === 'goal_1', '应该返回正确的目标')
  this.assert(goal.metadata, '应该包含元数据')
})

runner.test('_findGoalById - 查找不存在的目标', async function() {
  const goal = await didaGoal._findGoalById('nonexistent')

  this.assert(goal === null, '不存在的目标应该返回null')
})

runner.test('_generateMatchReason - 生成匹配原因', async function() {
  const reason = didaGoal._generateMatchReason(25, ['编程', '学习'], { title: '学习编程' })

  this.assert(typeof reason === 'string', '应该返回字符串')
  this.assert(reason.includes('关键词匹配'), '应该包含关键词匹配说明')
  this.assert(reason.includes('项目关联匹配'), '应该包含项目关联匹配说明')
})

// 运行测试
if (require.main === module) {
  runner.run().then(success => {
    process.exit(success ? 0 : 1)
  })
}

module.exports = runner
