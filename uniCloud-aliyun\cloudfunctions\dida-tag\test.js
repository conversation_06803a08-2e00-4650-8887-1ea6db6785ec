/**
 * dida-tag 云函数测试
 * 
 * 测试目标：
 * 1. 验证标签 CRUD 操作的完整性
 * 2. 验证标签高级操作（重命名、合并）
 * 3. 确保与 MCP 实现的 API 兼容性
 * 4. 验证 dida-base 云函数的集成
 * 
 * 测试覆盖：
 * - 标签：getTags, createTag, updateTag, deleteTag
 * - 高级操作：renameTag, mergeTags
 * - 错误处理和边界条件
 * - API 响应格式兼容性
 * 
 * <AUTHOR> 开发团队
 * @version 1.0.0
 * @since 2024-01-01
 */

// 模拟 uniCloud 环境
const mockUniCloud = {
  database: () => ({
    collection: (name) => ({
      where: () => ({
        orderBy: () => ({
          limit: () => ({
            get: async () => ({ data: [] })
          })
        })
      }),
      add: async (data) => ({ id: 'mock_id_' + Date.now() })
    })
  }),
  importObject: (name) => {
    if (name === 'dida-base') {
      return {
        makeRequest: async (url, method, data) => {
          // 模拟 API 响应
          if (url === '/api/v2/batch/check/0') {
            return {
              success: true,
              data: {
                tags: [
                  { id: 'tag_1', name: '工作', color: '#FF0000', sortOrder: 1 },
                  { id: 'tag_2', name: '学习', color: '#00FF00', sortOrder: 2 },
                  { id: 'tag_3', name: '生活', color: '#0000FF', sortOrder: 3 }
                ]
              }
            }
          }
          
          if (method === 'POST' && url === '/api/v2/tag') {
            return {
              success: true,
              data: { id: 'new_tag_' + Date.now(), ...data }
            }
          }
          
          if (method === 'POST' && url.includes('/api/v2/tag/')) {
            return {
              success: true,
              data: { id: url.split('/').pop(), ...data }
            }
          }
          
          if (method === 'DELETE' && url.includes('/api/v2/tag/')) {
            return {
              success: true,
              data: { message: '标签删除成功' }
            }
          }
          
          return { success: true, data: {} }
        },
        handleError: (error, code, message) => ({
          success: false,
          error: { code, message, details: error.message }
        }),
        formatResponse: (data) => ({
          success: true,
          data
        })
      }
    }
    return {}
  }
}

// 设置全局 uniCloud
global.uniCloud = mockUniCloud

// 导入要测试的云函数
const didaTag = require('./index.obj.js')

/**
 * 测试工具函数
 */
class TestRunner {
  constructor() {
    this.tests = []
    this.passed = 0
    this.failed = 0
  }

  /**
   * 添加测试用例
   */
  test(name, testFn) {
    this.tests.push({ name, testFn })
  }

  /**
   * 断言函数
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message || '断言失败')
    }
  }

  /**
   * 运行所有测试
   */
  async run() {
    console.log('🚀 开始运行 dida-tag 标签管理测试...\n')

    for (const { name, testFn } of this.tests) {
      try {
        console.log(`📋 运行测试: ${name}`)
        await testFn.call(this)
        this.passed++
        console.log(`✅ 测试通过: ${name}\n`)
      } catch (error) {
        this.failed++
        console.error(`❌ 测试失败: ${name}`)
        console.error(`   错误: ${error.message}\n`)
      }
    }

    console.log('📊 测试结果汇总:')
    console.log(`   ✅ 通过: ${this.passed}`)
    console.log(`   ❌ 失败: ${this.failed}`)
    console.log(`   📈 成功率: ${((this.passed / (this.passed + this.failed)) * 100).toFixed(1)}%`)

    return this.failed === 0
  }
}

// 创建测试运行器
const runner = new TestRunner()

// ==================== 标签 CRUD 测试 ====================

runner.test('getTags - 获取所有标签', async function() {
  const result = await didaTag.getTags()
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(Array.isArray(result.data), '应该返回标签数组')
  this.assert(result.data.length >= 0, '标签数组应该有效')
  
  // 验证标签数据结构
  if (result.data.length > 0) {
    const tag = result.data[0]
    this.assert(tag.id, '标签应该有ID')
    this.assert(tag.name, '标签应该有名称')
  }
})

runner.test('createTag - 创建基础标签', async function() {
  const result = await didaTag.createTag({
    name: '测试标签'
  })
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(result.data.id, '应该返回标签ID')
})

runner.test('createTag - 创建带颜色的标签', async function() {
  const result = await didaTag.createTag({
    name: '彩色标签',
    color: '#FF0000'
  })
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(result.data.id, '应该返回标签ID')
})

runner.test('createTag - 参数验证', async function() {
  const result = await didaTag.createTag({})
  
  this.assert(result.success === false, '缺少标签名称应该返回失败')
  this.assert(result.error.code === 'TAG_NAME_REQUIRED', '应该返回正确的错误代码')
})

runner.test('updateTag - 更新标签名称', async function() {
  const result = await didaTag.updateTag({
    tag_id_or_name: '工作',
    name: '工作任务'
  })
  
  this.assert(result.success === true, '应该返回成功状态')
})

runner.test('updateTag - 更新标签颜色', async function() {
  const result = await didaTag.updateTag({
    tag_id_or_name: '学习',
    color: '#FF00FF'
  })
  
  this.assert(result.success === true, '应该返回成功状态')
})

runner.test('updateTag - 标签不存在', async function() {
  const result = await didaTag.updateTag({
    tag_id_or_name: '不存在的标签',
    name: '新名称'
  })
  
  this.assert(result.success === false, '不存在的标签应该返回失败')
  this.assert(result.error.code === 'TAG_NOT_FOUND', '应该返回正确的错误代码')
})

runner.test('updateTag - 无更新字段', async function() {
  const result = await didaTag.updateTag({
    tag_id_or_name: '工作'
  })
  
  this.assert(result.success === true, '无更新字段应该返回原标签')
})

runner.test('deleteTag - 删除标签', async function() {
  const result = await didaTag.deleteTag({
    tag_id_or_name: '生活'
  })
  
  this.assert(result.success === true, '应该返回成功状态')
})

runner.test('deleteTag - 参数验证', async function() {
  const result = await didaTag.deleteTag({})
  
  this.assert(result.success === false, '缺少标签ID应该返回失败')
  this.assert(result.error.code === 'TAG_ID_REQUIRED', '应该返回正确的错误代码')
})

runner.test('deleteTag - 标签不存在', async function() {
  const result = await didaTag.deleteTag({
    tag_id_or_name: '不存在的标签'
  })
  
  this.assert(result.success === false, '不存在的标签应该返回失败')
  this.assert(result.error.code === 'TAG_NOT_FOUND', '应该返回正确的错误代码')
})

// ==================== 标签高级操作测试 ====================

runner.test('renameTag - 重命名标签', async function() {
  const result = await didaTag.renameTag({
    old_name: '工作',
    new_name: '办公'
  })
  
  this.assert(result.success === true, '应该返回成功状态')
})

runner.test('renameTag - 参数验证', async function() {
  const result = await didaTag.renameTag({
    old_name: '工作'
  })
  
  this.assert(result.success === false, '缺少新名称应该返回失败')
  this.assert(result.error.code === 'TAG_NAMES_REQUIRED', '应该返回正确的错误代码')
})

runner.test('renameTag - 旧标签不存在', async function() {
  const result = await didaTag.renameTag({
    old_name: '不存在的标签',
    new_name: '新名称'
  })
  
  this.assert(result.success === false, '旧标签不存在应该返回失败')
  this.assert(result.error.code === 'OLD_TAG_NOT_FOUND', '应该返回正确的错误代码')
})

runner.test('mergeTags - 合并标签', async function() {
  const result = await didaTag.mergeTags({
    source_name: '学习',
    target_name: '工作'
  })
  
  this.assert(result.success === true, '应该返回成功状态')
  this.assert(result.data.message, '应该返回合并消息')
})

runner.test('mergeTags - 参数验证', async function() {
  const result = await didaTag.mergeTags({
    source_name: '学习'
  })
  
  this.assert(result.success === false, '缺少目标标签应该返回失败')
  this.assert(result.error.code === 'TAG_NAMES_REQUIRED', '应该返回正确的错误代码')
})

runner.test('mergeTags - 源标签不存在', async function() {
  const result = await didaTag.mergeTags({
    source_name: '不存在的标签',
    target_name: '工作'
  })
  
  this.assert(result.success === false, '源标签不存在应该返回失败')
  this.assert(result.error.code === 'SOURCE_TAG_NOT_FOUND', '应该返回正确的错误代码')
})

runner.test('mergeTags - 目标标签不存在', async function() {
  const result = await didaTag.mergeTags({
    source_name: '学习',
    target_name: '不存在的标签'
  })
  
  this.assert(result.success === false, '目标标签不存在应该返回失败')
  this.assert(result.error.code === 'TARGET_TAG_NOT_FOUND', '应该返回正确的错误代码')
})

// ==================== 辅助方法测试 ====================

runner.test('_findTagByIdOrName - 按ID查找', async function() {
  const tag = await didaTag._findTagByIdOrName('tag_1')
  
  this.assert(tag !== null, '应该找到标签')
  this.assert(tag.id === 'tag_1', '应该返回正确的标签')
})

runner.test('_findTagByIdOrName - 按名称查找', async function() {
  const tag = await didaTag._findTagByIdOrName('工作')
  
  this.assert(tag !== null, '应该找到标签')
  this.assert(tag.name === '工作', '应该返回正确的标签')
})

runner.test('_findTagByIdOrName - 标签不存在', async function() {
  const tag = await didaTag._findTagByIdOrName('不存在的标签')
  
  this.assert(tag === null, '不存在的标签应该返回null')
})

// 运行测试
if (require.main === module) {
  runner.run().then(success => {
    process.exit(success ? 0 : 1)
  })
}

module.exports = runner
